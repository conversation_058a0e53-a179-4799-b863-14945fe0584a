const Wy = "proxyIpList",
      LX = Y({
          __name: "IpSetting",
          setup(e) {
              const {
                  ipcRenderer: t
              } = window;

              function n() {
                  return t.invoke("get-store", Wy).then(C => C || [])
              }

              function o(C) {
                  const w = JSON.parse(JSON.stringify(C));
                  return t.invoke("set-store", Wy, w)
              }
              const l = P([]),
                  a = P(!1),
                  r = P("add"),
                  i = Et({
                      name: "",
                      ip: "",
                      port: "",
                      user: "",
                      pass: "",
                      protocol: "http"
                  }),
                  u = P(!1),
                  c = P("");

              function d(C) {
                  const w = C.map(E => Number(E.name.replace("ip", ""))).filter(E => !isNaN(E));
                  return `ip${(w.length?Math.max(...w):0)+1}`
              }

              function f() {
                  Object.assign(i, {
                      name: d(l.value),
                      ip: "",
                      port: "",
                      user: "",
                      pass: "",
                      protocol: "http"
                  })
              }

              function h(C) {
                  return C.name ? /^[a-zA-Z0-9_-]+$/.test(C.name) ? C.ip ? /^([a-zA-Z0-9\-\.]+|\d{1,3}(\.\d{1,3}){3})$/.test(C.ip) ? C.port ? /^\d{1,5}$/.test(C.port) ? ["http", "socks5"].includes(C.protocol) ? l.value.some((w, _) => w.name === C.name && _ !== p.value) ? "IP名称不能重复" : !0 : "协议类型错误" : "端口格式不正确" : "端口不能为空" : "IP格式不正确" : "IP不能为空" : "IP名称仅支持字母、数字、下划线和中划线" : "IP名称不能为空"
              }
              const p = P(-1);

              function m() {
                  r.value = "add", f(), p.value = -1, a.value = !0
              }

              function v(C, w) {
                  r.value = "edit", Object.assign(i, C), p.value = w, a.value = !0
              }
              async function g() {
                  const C = h(i);
                  if (C !== !0) {
                      Xt.error(C);
                      return
                  }
                  r.value === "add" ? l.value.push({
                      ...i
                  }) : r.value === "edit" && l.value.splice(p.value, 1, {
                      ...i
                  }), await o(l.value), a.value = !1, Xt.success("保存成功")
              }
              async function b(C, w) {
                  await ei.confirm("确定要删除该代理IP吗？", "提示", {
                      type: "warning"
                  }), l.value.splice(w, 1), await o(l.value), Xt.success("删除成功")
              }

              function S() {
                  c.value = "", u.value = !0
              }
              async function y() {
                  const C = c.value.split(`
`).map(E => E.trim()).filter(Boolean);
                  if (!C.length) return Xt.error("内容不能为空");
                  let w = Math.max(0, ...l.value.map(E => Number(E.name.replace("ip", "")) || 0));
                  const _ = [];
                  for (let E of C) {
                      const [I, N, $, A, M] = E.split("|").map(L => L == null ? void 0 : L.trim());
                      w++;
                      const j = {
                              name: `ip${w}`,
                              ip: I || "",
                              port: N || "",
                              user: $ || "",
                              pass: A || "",
                              protocol: M === "socks5" ? "socks5" : "http"
                          },
                          H = h(j);
                      if (H !== !0) {
                          Xt.error(`第${w}行格式错误：${H}`);
                          return
                      }
                      _.push(j)
                  }
                  l.value.push(..._), await o(l.value), u.value = !1, Xt.success("批量添加成功")
              }
              return rt(async () => {
                  const C = await n();
                  Array.isArray(C) && (l.value = C)
              }), (C, w) => {
                  const _ = nt("el-button"),
                      E = nt("el-row"),
                      I = nt("el-table-column"),
                      N = nt("el-table"),
                      $ = nt("el-input"),
                      A = nt("el-form-item"),
                      M = nt("el-option"),
                      j = nt("el-select"),
                      H = nt("el-form"),
                      L = nt("el-dialog");
                  return T(), F("div", null, [B(E, {
                      gutter: 8,
                      style: {
                          "margin-bottom": "10px"
                      }
                  }, {
                      default: W(() => [B(_, {
                          type: "primary",
                          onClick: m
                      }, {
                          default: W(() => w[11] || (w[11] = [Ye("新增")])),
                          _: 1,
                          __: [11]
                      }), B(_, {
                          type: "primary",
                          onClick: S
                      }, {
                          default: W(() => w[12] || (w[12] = [Ye("批量新增")])),
                          _: 1,
                          __: [12]
                      })]),
                      _: 1
                  }), B(N, {
                      data: l.value,
                      border: "",
                      style: {
                          width: "100%",
                          height: "calc(100vh - 150px)",
                          "overflow-y": "auto"
                      }
                  }, {
                      default: W(() => [B(I, {
                          prop: "name",
                          label: "IP名称"
                      }), B(I, {
                          prop: "ip",
                          label: "IP"
                      }), B(I, {
                          prop: "port",
                          label: "端口"
                      }), B(I, {
                          prop: "user",
                          label: "账号"
                      }), B(I, {
                          prop: "pass",
                          label: "密码"
                      }, {
                          default: W(({
                              row: x
                          }) => [z("span", null, be(x.pass ? "*****" : ""), 1)]),
                          _: 1
                      }), B(I, {
                          prop: "protocol",
                          label: "协议类型"
                      }), B(I, {
                          label: "操作",
                          fixed: "right"
                      }, {
                          default: W(({
                              row: x,
                              $index: V
                          }) => [B(_, {
                              onClick: R => v(x, V),
                              size: "small",
                              type: "primary",
                              link: ""
                          }, {
                              default: W(() => w[13] || (w[13] = [Ye("编辑")])),
                              _: 2,
                              __: [13]
                          }, 1032, ["onClick"]), B(_, {
                              onClick: R => b(x, V),
                              size: "small",
                              type: "danger",
                              link: ""
                          }, {
                              default: W(() => w[14] || (w[14] = [Ye("删除")])),
                              _: 2,
                              __: [14]
                          }, 1032, ["onClick"])]),
                          _: 1
                      })]),
                      _: 1
                  }, 8, ["data"]), B(L, {
                      modelValue: a.value,
                      "onUpdate:modelValue": w[7] || (w[7] = x => a.value = x),
                      title: r.value === "add" ? "新增IP" : "编辑IP",
                      width: "400px"
                  }, {
                      footer: W(() => [B(_, {
                          onClick: w[6] || (w[6] = x => a.value = !1)
                      }, {
                          default: W(() => w[15] || (w[15] = [Ye("取消")])),
                          _: 1,
                          __: [15]
                      }), B(_, {
                          type: "primary",
                          onClick: g
                      }, {
                          default: W(() => w[16] || (w[16] = [Ye("确定")])),
                          _: 1,
                          __: [16]
                      })]),
                      default: W(() => [B(H, {
                          model: i,
                          "label-width": "80px"
                      }, {
                          default: W(() => [B(A, {
                              label: "IP名称"
                          }, {
                              default: W(() => [B($, {
                                  modelValue: i.name,
                                  "onUpdate:modelValue": w[0] || (w[0] = x => i.name = x),
                                  disabled: r.value === "edit"
                              }, null, 8, ["modelValue", "disabled"])]),
                              _: 1
                          }), B(A, {
                              label: "IP"
                          }, {
                              default: W(() => [B($, {
                                  modelValue: i.ip,
                                  "onUpdate:modelValue": w[1] || (w[1] = x => i.ip = x),
                                  autocomplete: "off"
                              }, null, 8, ["modelValue"])]),
                              _: 1
                          }), B(A, {
                              label: "端口"
                          }, {
                              default: W(() => [B($, {
                                  modelValue: i.port,
                                  "onUpdate:modelValue": w[2] || (w[2] = x => i.port = x),
                                  autocomplete: "off"
                              }, null, 8, ["modelValue"])]),
                              _: 1
                          }), B(A, {
                              label: "账号"
                          }, {
                              default: W(() => [B($, {
                                  modelValue: i.user,
                                  "onUpdate:modelValue": w[3] || (w[3] = x => i.user = x),
                                  autocomplete: "off"
                              }, null, 8, ["modelValue"])]),
                              _: 1
                          }), B(A, {
                              label: "密码"
                          }, {
                              default: W(() => [B($, {
                                  modelValue: i.pass,
                                  "onUpdate:modelValue": w[4] || (w[4] = x => i.pass = x),
                                  autocomplete: "off",
                                  "show-password": ""
                              }, null, 8, ["modelValue"])]),
                              _: 1
                          }), B(A, {
                              label: "协议类型"
                          }, {
                              default: W(() => [B(j, {
                                  modelValue: i.protocol,
                                  "onUpdate:modelValue": w[5] || (w[5] = x => i.protocol = x),
                                  placeholder: "请选择"
                              }, {
                                  default: W(() => [B(M, {
                                      label: "http",
                                      value: "http"
                                  }), B(M, {
                                      label: "socks5",
                                      value: "socks5"
                                  })]),
                                  _: 1
                              }, 8, ["modelValue"])]),
                              _: 1
                          })]),
                          _: 1
                      }, 8, ["model"])]),
                      _: 1
                  }, 8, ["modelValue", "title"]), B(L, {
                      modelValue: u.value,
                      "onUpdate:modelValue": w[10] || (w[10] = x => u.value = x),
                      title: "批量新增",
                      width: "500px"
                  }, {
                      footer: W(() => [B(_, {
                          onClick: w[9] || (w[9] = x => u.value = !1)
                      }, {
                          default: W(() => w[17] || (w[17] = [Ye("取消")])),
                          _: 1,
                          __: [17]
                      }), B(_, {
                          type: "primary",
                          onClick: y
                      }, {
                          default: W(() => w[18] || (w[18] = [Ye("确定")])),
                          _: 1,
                          __: [18]
                      })]),
                      default: W(() => [w[19] || (w[19] = z("div", {
                          style: {
                              "margin-bottom": "8px",
                              color: "#888",
                              "font-size": "13px"
                          }
                      }, [Ye(" 每行一个：ip|端口|账号|密码|http/socks5，账号/密码可留空。示例："), z("br"), Ye(" ***********|8080|user|pass|socks5 ")], -1)), B($, {
                          type: "textarea",
                          modelValue: c.value,
                          "onUpdate:modelValue": w[8] || (w[8] = x => c.value = x),
                          rows: 8,
                          placeholder: "一行一个，格式：ip|端口|账号|密码|http/socks5"
                      }, null, 8, ["modelValue"])]),
                      _: 1,
                      __: [19]
                  }, 8, ["modelValue"])])
              }
          }
      }),
      pm = (e, t) => {
          const n = e.__vccOpts || e;
          for (const [o, l] of t) n[o] = l;
          return n
      },
      DX = pm(LX, [
          ["__scopeId", "data-v-d1b6ea67"]
      ]),
      VX = {
          class: "table-scroll-area"
      },
      BX = {
          class: "cell-wrap"
      },
      FX = {
          class: "fans-def"
      },
      zX = {
          class: "cell-wrap"
      },
      HX = {
          class: "f-red"
      },
      KX = {
          class: "cell-wrap"
      },
      WX = {
          class: "fans-def"
      },
      jX = {
          class: "cell-wrap"
      },
      UX = {
          class: "fans-num"
      },
      qX = {
          class: "fans-label"
      },
      YX = {
          class: "cell-wrap"
      },
      GX = {
          class: "fans-num"
      },
      XX = {
          class: "fans-label"
      },
      JX = {
          class: "cell-wrap"
      },
      ZX = {
          class: "fans-num"
      },
      QX = {
          class: "fans-label"
      },
      eJ = {
          class: "cell-wrap"
      },
      tJ = {
          class: "fans-num"
      },
      nJ = {
          class: "fans-label"
      },
      oJ = "accountList",
      lJ = {
          __name: "AccountTable",
          setup(e) {
              const t = P([]),
                  n = P(400);

              function o() {
                  const r = window.innerHeight,
                      c = Math.max(220, r - 120);
                  n.value = c
              }
              const l = async () => {
                  try {
                      const r = await window.ipcRenderer.invoke("get-store", oJ);
                      t.value = Array.isArray(r) ? r : []
                  } catch (r) {
                      console.error("加载账号数据失败", r), t.value = []
                  }
              };
              window.ipcRenderer.on("account-row-updated", (r, {
                  id: i,
                  newRow: u
              }) => {
                  const c = t.value.findIndex(d => d.id === i);
                  c !== -1 && (t.value[c] = {
                      ...t.value[c],
                      ...u
                  })
              });
              let a = null;
              return rt(() => {
                  l(), o(), window.addEventListener("resize", o), a = setInterval(l, 1 * 1e3)
              }), It(() => {
                  a && clearInterval(a), window.removeEventListener("resize", o)
              }), (r, i) => {
                  const u = nt("el-table-column"),
                      c = nt("el-table");
                  return T(), F("div", VX, [B(c, {
                      data: t.value,
                      style: {
                          width: "100%",
                          height: "calc(100vh - 150px)",
                          overflow: "auto"
                      },
                      border: !1,
                      size: "small"
                  }, {
                      default: W(() => [B(u, {
                          label: "窗口",
                          type: "index",
                          width: "50"
                      }), B(u, {
                          label: "抖音号",
                          align: "left",
                          width: "120"
                      }, {
                          default: W(({
                              row: d
                          }) => {
                              var f;
                              return [z("div", null, be(((f = d.options) == null ? void 0 : f.userName) || "--"), 1)]
                          }),
                          _: 1
                      }), B(u, {
                          label: "近期",
                          align: "center"
                      }, {
                          default: W(({
                              row: d
                          }) => {
                              var f;
                              return [z("div", BX, [z("span", FX, be(((f = d.options) == null ? void 0 : f.fensiNum) ?? "-"), 1), i[0] || (i[0] = z("br", null, null, -1))])]
                          }),
                          _: 1
                      }), B(u, {
                          label: "今日",
                          align: "center"
                      }, {
                          default: W(({
                              row: d
                          }) => {
                              var f;
                              return [z("div", zX, [z("span", HX, be(((f = d.options) == null ? void 0 : f.jintianFans) ?? "-"), 1), i[1] || (i[1] = z("br", null, null, -1))])]
                          }),
                          _: 1
                      }), B(u, {
                          label: "昨日",
                          align: "center"
                      }, {
                          default: W(({
                              row: d
                          }) => {
                              var f;
                              return [z("div", KX, [z("span", WX, be(((f = d.options) == null ? void 0 : f.zuotianFans) ?? "-"), 1), i[2] || (i[2] = z("br", null, null, -1))])]
                          }),
                          _: 1
                      }), B(u, {
                          label: "关注",
                          align: "center"
                      }, {
                          default: W(({
                              row: d
                          }) => {
                              var f, h, p, m, v, g;
                              return [z("div", jX, [z("span", UX, be(((p = (h = (f = d.options) == null ? void 0 : f.tongjiNum) == null ? void 0 : h.guanzhu) == null ? void 0 : p.cur) ?? "-"), 1), i[3] || (i[3] = z("br", null, null, -1)), z("span", qX, be(((g = (v = (m = d.options) == null ? void 0 : m.tongjiNum) == null ? void 0 : v.guanzhu) == null ? void 0 : g.total) ?? "-"), 1)])]
                          }),
                          _: 1
                      }), B(u, {
                          label: "点赞",
                          align: "center"
                      }, {
                          default: W(({
                              row: d
                          }) => {
                              var f, h, p, m, v, g;
                              return [z("div", YX, [z("span", GX, be(((p = (h = (f = d.options) == null ? void 0 : f.tongjiNum) == null ? void 0 : h.dianzan) == null ? void 0 : p.cur) ?? "-"), 1), i[4] || (i[4] = z("br", null, null, -1)), z("span", XX, be(((g = (v = (m = d.options) == null ? void 0 : m.tongjiNum) == null ? void 0 : v.dianzan) == null ? void 0 : g.total) ?? "-"), 1)])]
                          }),
                          _: 1
                      }), B(u, {
                          label: "收藏",
                          align: "center"
                      }, {
                          default: W(({
                              row: d
                          }) => {
                              var f, h, p, m, v, g;
                              return [z("div", JX, [z("span", ZX, be(((p = (h = (f = d.options) == null ? void 0 : f.tongjiNum) == null ? void 0 : h.shoucan) == null ? void 0 : p.cur) ?? "-"), 1), i[5] || (i[5] = z("br", null, null, -1)), z("span", QX, be(((g = (v = (m = d.options) == null ? void 0 : m.tongjiNum) == null ? void 0 : v.shoucan) == null ? void 0 : g.total) ?? "-"), 1)])]
                          }),
                          _: 1
                      }), B(u, {
                          label: "评论",
                          align: "center"
                      }, {
                          default: W(({
                              row: d
                          }) => {
                              var f, h, p, m, v, g;
                              return [z("div", eJ, [z("span", tJ, be(((p = (h = (f = d.options) == null ? void 0 : f.tongjiNum) == null ? void 0 : h.pinglun) == null ? void 0 : p.cur) ?? "-"), 1), i[6] || (i[6] = z("br", null, null, -1)), z("span", nJ, be(((g = (v = (m = d.options) == null ? void 0 : m.tongjiNum) == null ? void 0 : v.pinglun) == null ? void 0 : g.total) ?? "-"), 1)])]
                          }),
                          _: 1
                      }), B(u, {
                          prop: "status",
                          label: "日志",
                          align: "center",
                          width: "240"
                      }, {
                          default: W(({
                              row: d
                          }) => [Ye(be(d.options.status ?? "-"), 1)]),
                          _: 1
                      })]),
                      _: 1
                  }, 8, ["data"])])
              }
          }
      },
      aJ = pm(lJ, [
          ["__scopeId", "data-v-3c0cddaf"]
      ]);

  function Uk(e, t) {
      return function() {
          return e.apply(t, arguments)
      }
  }
  const {
      toString: rJ
  } = Object.prototype, {
      getPrototypeOf: vm
  } = Object, {
      iterator: Nd,
      toStringTag: qk
  } = Symbol, xd = (e => t => {
      const n = rJ.call(t);
      return e[n] || (e[n] = n.slice(8, -1).toLowerCase())
  })(Object.create(null)), Jo = e => (e = e.toLowerCase(), t => xd(t) === e), Id = e => t => typeof t === e, {
      isArray: Ss
  } = Array, Mi = Id("undefined");

  function sJ(e) {
      return e !== null && !Mi(e) && e.constructor !== null && !Mi(e.constructor) && ro(e.constructor.isBuffer) && e.constructor.isBuffer(e)
  }
  const Yk = Jo("ArrayBuffer");

  function iJ(e) {
      let t;
      return typeof ArrayBuffer < "u" && ArrayBuffer.isView ? t = ArrayBuffer.isView(e) : t = e && e.buffer && Yk(e.buffer), t
  }
  const uJ = Id("string"),
      ro = Id("function"),
      Gk = Id("number"),
      Md = e => e !== null && typeof e == "object",
      cJ = e => e === !0 || e === !1,
      Gu = e => {
          if (xd(e) !== "object") return !1;
          const t = vm(e);
          return (t === null || t === Object.prototype || Object.getPrototypeOf(t) === null) && !(qk in e) && !(Nd in e)
      },
      dJ = Jo("Date"),
      fJ = Jo("File"),
      pJ = Jo("Blob"),
      vJ = Jo("FileList"),
      hJ = e => Md(e) && ro(e.pipe),
      mJ = e => {
          let t;
          return e && (typeof FormData == "function" && e instanceof FormData || ro(e.append) && ((t = xd(e)) === "formdata" || t === "object" && ro(e.toString) && e.toString() === "[object FormData]"))
      },
      gJ = Jo("URLSearchParams"),
      [bJ, yJ, wJ, CJ] = ["ReadableStream", "Request", "Response", "Headers"].map(Jo),
      SJ = e => e.trim ? e.trim() : e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, "");

  function ou(e, t, {
      allOwnKeys: n = !1
  } = {}) {
      if (e === null || typeof e > "u") return;
      let o, l;
      if (typeof e != "object" && (e = [e]), Ss(e))
          for (o = 0, l = e.length; o < l; o++) t.call(null, e[o], o, e);
      else {
          const a = n ? Object.getOwnPropertyNames(e) : Object.keys(e),
              r = a.length;
          let i;
          for (o = 0; o < r; o++) i = a[o], t.call(null, e[i], i, e)
      }
  }

  function Xk(e, t) {
      t = t.toLowerCase();
      const n = Object.keys(e);
      let o = n.length,
          l;
      for (; o-- > 0;)
          if (l = n[o], t === l.toLowerCase()) return l;
      return null
  }
  const Ha = typeof globalThis < "u" ? globalThis : typeof self < "u" ? self : typeof window < "u" ? window : global,
      Jk = e => !Mi(e) && e !== Ha;

  function Gp() {
      const {
          caseless: e
      } = Jk(this) && this || {}, t = {}, n = (o, l) => {
          const a = e && Xk(t, l) || l;
          Gu(t[a]) && Gu(o) ? t[a] = Gp(t[a], o) : Gu(o) ? t[a] = Gp({}, o) : Ss(o) ? t[a] = o.slice() : t[a] = o
      };
      for (let o = 0, l = arguments.length; o < l; o++) arguments[o] && ou(arguments[o], n);
      return t
  }
  const _J = (e, t, n, {
          allOwnKeys: o
      } = {}) => (ou(t, (l, a) => {
          n && ro(l) ? e[a] = Uk(l, n) : e[a] = l
      }, {
          allOwnKeys: o
      }), e),
      kJ = e => (e.charCodeAt(0) === 65279 && (e = e.slice(1)), e),
      EJ = (e, t, n, o) => {
          e.prototype = Object.create(t.prototype, o), e.prototype.constructor = e, Object.defineProperty(e, "super", {
              value: t.prototype
          }), n && Object.assign(e.prototype, n)
      },
      TJ = (e, t, n, o) => {
          let l, a, r;
          const i = {};
          if (t = t || {}, e == null) return t;
          do {
              for (l = Object.getOwnPropertyNames(e), a = l.length; a-- > 0;) r = l[a], (!o || o(r, e, t)) && !i[r] && (t[r] = e[r], i[r] = !0);
              e = n !== !1 && vm(e)
          } while (e && (!n || n(e, t)) && e !== Object.prototype);
          return t
      },
      $J = (e, t, n) => {
          e = String(e), (n === void 0 || n > e.length) && (n = e.length), n -= t.length;
          const o = e.indexOf(t, n);
          return o !== -1 && o === n
      },
      OJ = e => {
          if (!e) return null;
          if (Ss(e)) return e;
          let t = e.length;
          if (!Gk(t)) return null;
          const n = new Array(t);
          for (; t-- > 0;) n[t] = e[t];
          return n
      },
      NJ = (e => t => e && t instanceof e)(typeof Uint8Array < "u" && vm(Uint8Array)),
      xJ = (e, t) => {
          const o = (e && e[Nd]).call(e);
          let l;
          for (;
              (l = o.next()) && !l.done;) {
              const a = l.value;
              t.call(e, a[0], a[1])
          }
      },
      IJ = (e, t) => {
          let n;
          const o = [];
          for (;
              (n = e.exec(t)) !== null;) o.push(n);
          return o
      },
      MJ = Jo("HTMLFormElement"),
      RJ = e => e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g, function(n, o, l) {
          return o.toUpperCase() + l
      }),
      jy = (({
          hasOwnProperty: e
      }) => (t, n) => e.call(t, n))(Object.prototype),
      PJ = Jo("RegExp"),
      Zk = (e, t) => {
          const n = Object.getOwnPropertyDescriptors(e),
              o = {};
          ou(n, (l, a) => {
              let r;
              (r = t(l, a, e)) !== !1 && (o[a] = r || l)
          }), Object.defineProperties(e, o)
      },
      AJ = e => {
          Zk(e, (t, n) => {
              if (ro(e) && ["arguments", "caller", "callee"].indexOf(n) !== -1) return !1;
              const o = e[n];
              if (ro(o)) {
                  if (t.enumerable = !1, "writable" in t) {
                      t.writable = !1;
                      return
                  }
                  t.set || (t.set = () => {
                      throw Error("Can not rewrite read-only method '" + n + "'")
                  })
              }
          })
      },
      LJ = (e, t) => {
          const n = {},
              o = l => {
                  l.forEach(a => {
                      n[a] = !0
                  })
              };
          return Ss(e) ? o(e) : o(String(e).split(t)), n
      },
      DJ = () => {},
      VJ = (e, t) => e != null && Number.isFinite(e = +e) ? e : t;

  function BJ(e) {
      return !!(e && ro(e.append) && e[qk] === "FormData" && e[Nd])
  }
  const FJ = e => {
          const t = new Array(10),
              n = (o, l) => {
                  if (Md(o)) {
                      if (t.indexOf(o) >= 0) return;
                      if (!("toJSON" in o)) {
                          t[l] = o;
                          const a = Ss(o) ? [] : {};
                          return ou(o, (r, i) => {
                              const u = n(r, l + 1);
                              !Mi(u) && (a[i] = u)
                          }), t[l] = void 0, a
                      }
                  }
                  return o
              };
          return n(e, 0)
      },
      zJ = Jo("AsyncFunction"),
      HJ = e => e && (Md(e) || ro(e)) && ro(e.then) && ro(e.catch),
      Qk = ((e, t) => e ? setImmediate : t ? ((n, o) => (Ha.addEventListener("message", ({
          source: l,
          data: a
      }) => {
          l === Ha && a === n && o.length && o.shift()()
      }, !1), l => {
          o.push(l), Ha.postMessage(n, "*")
      }))(`axios@${Math.random()}`, []) : n => setTimeout(n))(typeof setImmediate == "function", ro(Ha.postMessage)),
      KJ = typeof queueMicrotask < "u" ? queueMicrotask.bind(Ha) : typeof process < "u" && process.nextTick || Qk,
      WJ = e => e != null && ro(e[Nd]),
      Me = {
          isArray: Ss,
          isArrayBuffer: Yk,
          isBuffer: sJ,
          isFormData: mJ,
          isArrayBufferView: iJ,
          isString: uJ,
          isNumber: Gk,
          isBoolean: cJ,
          isObject: Md,
          isPlainObject: Gu,
          isReadableStream: bJ,
          isRequest: yJ,
          isResponse: wJ,
          isHeaders: CJ,
          isUndefined: Mi,
          isDate: dJ,
          isFile: fJ,
          isBlob: pJ,
          isRegExp: PJ,
          isFunction: ro,
          isStream: hJ,
          isURLSearchParams: gJ,
          isTypedArray: NJ,
          isFileList: vJ,
          forEach: ou,
          merge: Gp,
          extend: _J,
          trim: SJ,
          stripBOM: kJ,
          inherits: EJ,
          toFlatObject: TJ,
          kindOf: xd,
          kindOfTest: Jo,
          endsWith: $J,
          toArray: OJ,
          forEachEntry: xJ,
          matchAll: IJ,
          isHTMLForm: MJ,
          hasOwnProperty: jy,
          hasOwnProp: jy,
          reduceDescriptors: Zk,
          freezeMethods: AJ,
          toObjectSet: LJ,
          toCamelCase: RJ,
          noop: DJ,
          toFiniteNumber: VJ,
          findKey: Xk,
          global: Ha,
          isContextDefined: Jk,
          isSpecCompliantForm: BJ,
          toJSONObject: FJ,
          isAsyncFn: zJ,
          isThenable: HJ,
          setImmediate: Qk,
          asap: KJ,
          isIterable: WJ
      };

  function xt(e, t, n, o, l) {
      Error.call(this), Error.captureStackTrace ? Error.captureStackTrace(this, this.constructor) : this.stack = new Error().stack, this.message = e, this.name = "AxiosError", t && (this.code = t), n && (this.config = n), o && (this.request = o), l && (this.response = l, this.status = l.status ? l.status : null)
  }
  Me.inherits(xt, Error, {
      toJSON: function() {
          return {
              message: this.message,
              name: this.name,
              description: this.description,
              number: this.number,
              fileName: this.fileName,
              lineNumber: this.lineNumber,
              columnNumber: this.columnNumber,
              stack: this.stack,
              config: Me.toJSONObject(this.config),
              code: this.code,
              status: this.status
          }
      }
  });
  const e2 = xt.prototype,
      t2 = {};
  ["ERR_BAD_OPTION_VALUE", "ERR_BAD_OPTION", "ECONNABORTED", "ETIMEDOUT", "ERR_NETWORK", "ERR_FR_TOO_MANY_REDIRECTS", "ERR_DEPRECATED", "ERR_BAD_RESPONSE", "ERR_BAD_REQUEST", "ERR_CANCELED", "ERR_NOT_SUPPORT", "ERR_INVALID_URL"].forEach(e => {
      t2[e] = {
          value: e
      }
  });
  Object.defineProperties(xt, t2);
  Object.defineProperty(e2, "isAxiosError", {
      value: !0
  });
  xt.from = (e, t, n, o, l, a) => {
      const r = Object.create(e2);
      return Me.toFlatObject(e, r, function(u) {
          return u !== Error.prototype
      }, i => i !== "isAxiosError"), xt.call(r, e.message, t, n, o, l), r.cause = e, r.name = e.name, a && Object.assign(r, a), r
  };
  const jJ = null;

  function Xp(e) {
      return Me.isPlainObject(e) || Me.isArray(e)
  }

  function n2(e) {
      return Me.endsWith(e, "[]") ? e.slice(0, -2) : e
  }

  function Uy(e, t, n) {
      return e ? e.concat(t).map(function(l, a) {
          return l = n2(l), !n && a ? "[" + l + "]" : l
      }).join(n ? "." : "") : t
  }

  function UJ(e) {
      return Me.isArray(e) && !e.some(Xp)
  }
  const qJ = Me.toFlatObject(Me, {}, null, function(t) {
      return /^is[A-Z]/.test(t)
  });

  function Rd(e, t, n) {
      if (!Me.isObject(e)) throw new TypeError("target must be an object");
      t = t || new FormData, n = Me.toFlatObject(n, {
          metaTokens: !0,
          dots: !1,
          indexes: !1
      }, !1, function(v, g) {
          return !Me.isUndefined(g[v])
      });
      const o = n.metaTokens,
          l = n.visitor || d,
          a = n.dots,
          r = n.indexes,
          u = (n.Blob || typeof Blob < "u" && Blob) && Me.isSpecCompliantForm(t);
      if (!Me.isFunction(l)) throw new TypeError("visitor must be a function");

      function c(m) {
          if (m === null) return "";
          if (Me.isDate(m)) return m.toISOString();
          if (Me.isBoolean(m)) return m.toString();
          if (!u && Me.isBlob(m)) throw new xt("Blob is not supported. Use a Buffer instead.");
          return Me.isArrayBuffer(m) || Me.isTypedArray(m) ? u && typeof Blob == "function" ? new Blob([m]) : Buffer.from(m) : m
      }

      function d(m, v, g) {
          let b = m;
          if (m && !g && typeof m == "object") {
              if (Me.endsWith(v, "{}")) v = o ? v : v.slice(0, -2), m = JSON.stringify(m);
              else if (Me.isArray(m) && UJ(m) || (Me.isFileList(m) || Me.endsWith(v, "[]")) && (b = Me.toArray(m))) return v = n2(v), b.forEach(function(y, C) {
                  !(Me.isUndefined(y) || y === null) && t.append(r === !0 ? Uy([v], C, a) : r === null ? v : v + "[]", c(y))
              }), !1
          }
          return Xp(m) ? !0 : (t.append(Uy(g, v, a), c(m)), !1)
      }
      const f = [],
          h = Object.assign(qJ, {
              defaultVisitor: d,
              convertValue: c,
              isVisitable: Xp
          });

      function p(m, v) {
          if (!Me.isUndefined(m)) {
              if (f.indexOf(m) !== -1) throw Error("Circular reference detected in " + v.join("."));
              f.push(m), Me.forEach(m, function(b, S) {
                  (!(Me.isUndefined(b) || b === null) && l.call(t, b, Me.isString(S) ? S.trim() : S, v, h)) === !0 && p(b, v ? v.concat(S) : [S])
              }), f.pop()
          }
      }
      if (!Me.isObject(e)) throw new TypeError("data must be an object");
      return p(e), t
  }

  function qy(e) {
      const t = {
          "!": "%21",
          "'": "%27",
          "(": "%28",
          ")": "%29",
          "~": "%7E",
          "%20": "+",
          "%00": "\0"
      };
      return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g, function(o) {
          return t[o]
      })
  }

  function hm(e, t) {
      this._pairs = [], e && Rd(e, this, t)
  }
  const o2 = hm.prototype;
  o2.append = function(t, n) {
      this._pairs.push([t, n])
  };
  o2.toString = function(t) {
      const n = t ? function(o) {
          return t.call(this, o, qy)
      } : qy;
      return this._pairs.map(function(l) {
          return n(l[0]) + "=" + n(l[1])
      }, "").join("&")
  };

  function YJ(e) {
      return encodeURIComponent(e).replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(/%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]")
  }

  function l2(e, t, n) {
      if (!t) return e;
      const o = n && n.encode || YJ;
      Me.isFunction(n) && (n = {
          serialize: n
      });
      const l = n && n.serialize;
      let a;
      if (l ? a = l(t, n) : a = Me.isURLSearchParams(t) ? t.toString() : new hm(t, n).toString(o), a) {
          const r = e.indexOf("#");
          r !== -1 && (e = e.slice(0, r)), e += (e.indexOf("?") === -1 ? "?" : "&") + a
      }
      return e
  }
  class Yy {
      constructor() {
          this.handlers = []
      }
      use(t, n, o) {
          return this.handlers.push({
              fulfilled: t,
              rejected: n,
              synchronous: o ? o.synchronous : !1,
              runWhen: o ? o.runWhen : null
          }), this.handlers.length - 1
      }
      eject(t) {
          this.handlers[t] && (this.handlers[t] = null)
      }
      clear() {
          this.handlers && (this.handlers = [])
      }
      forEach(t) {
          Me.forEach(this.handlers, function(o) {
              o !== null && t(o)
          })
      }
  }
  const a2 = {
          silentJSONParsing: !0,
          forcedJSONParsing: !0,
          clarifyTimeoutError: !1
      },
      GJ = typeof URLSearchParams < "u" ? URLSearchParams : hm,
      XJ = typeof FormData < "u" ? FormData : null,
      JJ = typeof Blob < "u" ? Blob : null,
      ZJ = {
          isBrowser: !0,
          classes: {
              URLSearchParams: GJ,
              FormData: XJ,
              Blob: JJ
          },
          protocols: ["http", "https", "file", "blob", "url", "data"]
      },
      mm = typeof window < "u" && typeof document < "u",
      Jp = typeof navigator == "object" && navigator || void 0,
      QJ = mm && (!Jp || ["ReactNative", "NativeScript", "NS"].indexOf(Jp.product) < 0),
      eZ = typeof WorkerGlobalScope < "u" && self instanceof WorkerGlobalScope && typeof self.importScripts == "function",
      tZ = mm && window.location.href || "http://localhost",
      nZ = Object.defineProperty({
          __proto__: null,
          hasBrowserEnv: mm,
          hasStandardBrowserEnv: QJ,
          hasStandardBrowserWebWorkerEnv: eZ,
          navigator: Jp,
          origin: tZ
      }, Symbol.toStringTag, {
          value: "Module"
      }),
      Dn = {
          ...nZ,
          ...ZJ
      };

  function oZ(e, t) {
      return Rd(e, new Dn.classes.URLSearchParams, Object.assign({
          visitor: function(n, o, l, a) {
              return Dn.isNode && Me.isBuffer(n) ? (this.append(o, n.toString("base64")), !1) : a.defaultVisitor.apply(this, arguments)
          }
      }, t))
  }

  function lZ(e) {
      return Me.matchAll(/\w+|\[(\w*)]/g, e).map(t => t[0] === "[]" ? "" : t[1] || t[0])
  }

  function aZ(e) {
      const t = {},
          n = Object.keys(e);
      let o;
      const l = n.length;
      let a;
      for (o = 0; o < l; o++) a = n[o], t[a] = e[a];
      return t
  }

  function r2(e) {
      function t(n, o, l, a) {
          let r = n[a++];
          if (r === "__proto__") return !0;
          const i = Number.isFinite(+r),
              u = a >= n.length;
          return r = !r && Me.isArray(l) ? l.length : r, u ? (Me.hasOwnProp(l, r) ? l[r] = [l[r], o] : l[r] = o, !i) : ((!l[r] || !Me.isObject(l[r])) && (l[r] = []), t(n, o, l[r], a) && Me.isArray(l[r]) && (l[r] = aZ(l[r])), !i)
      }
      if (Me.isFormData(e) && Me.isFunction(e.entries)) {
          const n = {};
          return Me.forEachEntry(e, (o, l) => {
              t(lZ(o), l, n, 0)
          }), n
      }
      return null
  }

  function rZ(e, t, n) {
      if (Me.isString(e)) try {
          return (t || JSON.parse)(e), Me.trim(e)
      } catch (o) {
          if (o.name !== "SyntaxError") throw o
      }
      return (n || JSON.stringify)(e)
  }
  const lu = {
      transitional: a2,
      adapter: ["xhr", "http", "fetch"],
      transformRequest: [function(t, n) {
          const o = n.getContentType() || "",
              l = o.indexOf("application/json") > -1,
              a = Me.isObject(t);
          if (a && Me.isHTMLForm(t) && (t = new FormData(t)), Me.isFormData(t)) return l ? JSON.stringify(r2(t)) : t;
          if (Me.isArrayBuffer(t) || Me.isBuffer(t) || Me.isStream(t) || Me.isFile(t) || Me.isBlob(t) || Me.isReadableStream(t)) return t;
          if (Me.isArrayBufferView(t)) return t.buffer;
          if (Me.isURLSearchParams(t)) return n.setContentType("application/x-www-form-urlencoded;charset=utf-8", !1), t.toString();
          let i;
          if (a) {
              if (o.indexOf("application/x-www-form-urlencoded") > -1) return oZ(t, this.formSerializer).toString();
              if ((i = Me.isFileList(t)) || o.indexOf("multipart/form-data") > -1) {
                  const u = this.env && this.env.FormData;
                  return Rd(i ? {
                      "files[]": t
                  } : t, u && new u, this.formSerializer)
              }
          }
          return a || l ? (n.setContentType("application/json", !1), rZ(t)) : t
      }],
      transformResponse: [function(t) {
          const n = this.transitional || lu.transitional,
              o = n && n.forcedJSONParsing,
              l = this.responseType === "json";
          if (Me.isResponse(t) || Me.isReadableStream(t)) return t;
          if (t && Me.isString(t) && (o && !this.responseType || l)) {
              const r = !(n && n.silentJSONParsing) && l;
              try {
                  return JSON.parse(t)
              } catch (i) {
                  if (r) throw i.name === "SyntaxError" ? xt.from(i, xt.ERR_BAD_RESPONSE, this, null, this.response) : i
              }
          }
          return t
      }],
      timeout: 0,
      xsrfCookieName: "XSRF-TOKEN",
      xsrfHeaderName: "X-XSRF-TOKEN",
      maxContentLength: -1,
      maxBodyLength: -1,
      env: {
          FormData: Dn.classes.FormData,
          Blob: Dn.classes.Blob
      },
      validateStatus: function(t) {
          return t >= 200 && t < 300
      },
      headers: {
          common: {
              Accept: "application/json, text/plain, */*",
              "Content-Type": void 0
          }
      }
  };
  Me.forEach(["delete", "get", "head", "post", "put", "patch"], e => {
      lu.headers[e] = {}
  });
  const sZ = Me.toObjectSet(["age", "authorization", "content-length", "content-type", "etag", "expires", "from", "host", "if-modified-since", "if-unmodified-since", "last-modified", "location", "max-forwards", "proxy-authorization", "referer", "retry-after", "user-agent"]),
      iZ = e => {
          const t = {};
          let n, o, l;
          return e && e.split(`
`).forEach(function(r) {
              l = r.indexOf(":"), n = r.substring(0, l).trim().toLowerCase(), o = r.substring(l + 1).trim(), !(!n || t[n] && sZ[n]) && (n === "set-cookie" ? t[n] ? t[n].push(o) : t[n] = [o] : t[n] = t[n] ? t[n] + ", " + o : o)
          }), t
      },
      Gy = Symbol("internals");

  function Ms(e) {
      return e && String(e).trim().toLowerCase()
  }

  function Xu(e) {
      return e === !1 || e == null ? e : Me.isArray(e) ? e.map(Xu) : String(e)
  }

  function uZ(e) {
      const t = Object.create(null),
          n = /([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;
      let o;
      for (; o = n.exec(e);) t[o[1]] = o[2];
      return t
  }
  const cZ = e => /^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());

  function Tf(e, t, n, o, l) {
      if (Me.isFunction(o)) return o.call(this, t, n);
      if (l && (t = n), !!Me.isString(t)) {
          if (Me.isString(o)) return t.indexOf(o) !== -1;
          if (Me.isRegExp(o)) return o.test(t)
      }
  }

  function dZ(e) {
      return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g, (t, n, o) => n.toUpperCase() + o)
  }

  function fZ(e, t) {
      const n = Me.toCamelCase(" " + t);
      ["get", "set", "has"].forEach(o => {
          Object.defineProperty(e, o + n, {
              value: function(l, a, r) {
                  return this[o].call(this, t, l, a, r)
              },
              configurable: !0
          })
      })
  }
  let so = class {
      constructor(t) {
          t && this.set(t)
      }
      set(t, n, o) {
          const l = this;

          function a(i, u, c) {
              const d = Ms(u);
              if (!d) throw new Error("header name must be a non-empty string");
              const f = Me.findKey(l, d);
              (!f || l[f] === void 0 || c === !0 || c === void 0 && l[f] !== !1) && (l[f || u] = Xu(i))
          }
          const r = (i, u) => Me.forEach(i, (c, d) => a(c, d, u));
          if (Me.isPlainObject(t) || t instanceof this.constructor) r(t, n);
          else if (Me.isString(t) && (t = t.trim()) && !cZ(t)) r(iZ(t), n);
          else if (Me.isObject(t) && Me.isIterable(t)) {
              let i = {},
                  u, c;
              for (const d of t) {
                  if (!Me.isArray(d)) throw TypeError("Object iterator must return a key-value pair");
                  i[c = d[0]] = (u = i[c]) ? Me.isArray(u) ? [...u, d[1]] : [u, d[1]] : d[1]
              }
              r(i, n)
          } else t != null && a(n, t, o);
          return this
      }
      get(t, n) {
          if (t = Ms(t), t) {
              const o = Me.findKey(this, t);
              if (o) {
                  const l = this[o];
                  if (!n) return l;
                  if (n === !0) return uZ(l);
                  if (Me.isFunction(n)) return n.call(this, l, o);
                  if (Me.isRegExp(n)) return n.exec(l);
                  throw new TypeError("parser must be boolean|regexp|function")
              }
          }
      }
      has(t, n) {
          if (t = Ms(t), t) {
              const o = Me.findKey(this, t);
              return !!(o && this[o] !== void 0 && (!n || Tf(this, this[o], o, n)))
          }
          return !1
      }
      delete(t, n) {
          const o = this;
          let l = !1;

          function a(r) {
              if (r = Ms(r), r) {
                  const i = Me.findKey(o, r);
                  i && (!n || Tf(o, o[i], i, n)) && (delete o[i], l = !0)
              }
          }
          return Me.isArray(t) ? t.forEach(a) : a(t), l
      }
      clear(t) {
          const n = Object.keys(this);
          let o = n.length,
              l = !1;
          for (; o--;) {
              const a = n[o];
              (!t || Tf(this, this[a], a, t, !0)) && (delete this[a], l = !0)
          }
          return l
      }
      normalize(t) {
          const n = this,
              o = {};
          return Me.forEach(this, (l, a) => {
              const r = Me.findKey(o, a);
              if (r) {
                  n[r] = Xu(l), delete n[a];
                  return
              }
              const i = t ? dZ(a) : String(a).trim();
              i !== a && delete n[a], n[i] = Xu(l), o[i] = !0
          }), this
      }
      concat(...t) {
          return this.constructor.concat(this, ...t)
      }
      toJSON(t) {
          const n = Object.create(null);
          return Me.forEach(this, (o, l) => {
              o != null && o !== !1 && (n[l] = t && Me.isArray(o) ? o.join(", ") : o)
          }), n
      } [Symbol.iterator]() {
          return Object.entries(this.toJSON())[Symbol.iterator]()
      }
      toString() {
          return Object.entries(this.toJSON()).map(([t, n]) => t + ": " + n).join(`
`)
      }
      getSetCookie() {
          return this.get("set-cookie") || []
      }
      get[Symbol.toStringTag]() {
          return "AxiosHeaders"
      }
      static from(t) {
          return t instanceof this ? t : new this(t)
      }
      static concat(t, ...n) {
          const o = new this(t);
          return n.forEach(l => o.set(l)), o
      }
      static accessor(t) {
          const o = (this[Gy] = this[Gy] = {
                  accessors: {}
              }).accessors,
              l = this.prototype;

          function a(r) {
              const i = Ms(r);
              o[i] || (fZ(l, r), o[i] = !0)
          }
          return Me.isArray(t) ? t.forEach(a) : a(t), this
      }
  };
  so.accessor(["Content-Type", "Content-Length", "Accept", "Accept-Encoding", "User-Agent", "Authorization"]);
  Me.reduceDescriptors(so.prototype, ({
      value: e
  }, t) => {
      let n = t[0].toUpperCase() + t.slice(1);
      return {
          get: () => e,
          set(o) {
              this[n] = o
          }
      }
  });
  Me.freezeMethods(so);

  function $f(e, t) {
      const n = this || lu,
          o = t || n,
          l = so.from(o.headers);
      let a = o.data;
      return Me.forEach(e, function(i) {
          a = i.call(n, a, l.normalize(), t ? t.status : void 0)
      }), l.normalize(), a
  }

  function s2(e) {
      return !!(e && e.__CANCEL__)
  }

  function _s(e, t, n) {
      xt.call(this, e ?? "canceled", xt.ERR_CANCELED, t, n), this.name = "CanceledError"
  }
  Me.inherits(_s, xt, {
      __CANCEL__: !0
  });

  function i2(e, t, n) {
      const o = n.config.validateStatus;
      !n.status || !o || o(n.status) ? e(n) : t(new xt("Request failed with status code " + n.status, [xt.ERR_BAD_REQUEST, xt.ERR_BAD_RESPONSE][Math.floor(n.status / 100) - 4], n.config, n.request, n))
  }

  function pZ(e) {
      const t = /^([-+\w]{1,25})(:?\/\/|:)/.exec(e);
      return t && t[1] || ""
  }

  function vZ(e, t) {
      e = e || 10;
      const n = new Array(e),
          o = new Array(e);
      let l = 0,
          a = 0,
          r;
      return t = t !== void 0 ? t : 1e3,
          function(u) {
              const c = Date.now(),
                  d = o[a];
              r || (r = c), n[l] = u, o[l] = c;
              let f = a,
                  h = 0;
              for (; f !== l;) h += n[f++], f = f % e;
              if (l = (l + 1) % e, l === a && (a = (a + 1) % e), c - r < t) return;
              const p = d && c - d;
              return p ? Math.round(h * 1e3 / p) : void 0
          }
  }

  function hZ(e, t) {
      let n = 0,
          o = 1e3 / t,
          l, a;
      const r = (c, d = Date.now()) => {
          n = d, l = null, a && (clearTimeout(a), a = null), e.apply(null, c)
      };
      return [(...c) => {
          const d = Date.now(),
              f = d - n;
          f >= o ? r(c, d) : (l = c, a || (a = setTimeout(() => {
              a = null, r(l)
          }, o - f)))
      }, () => l && r(l)]
  }
  const Lc = (e, t, n = 3) => {
          let o = 0;
          const l = vZ(50, 250);
          return hZ(a => {
              const r = a.loaded,
                  i = a.lengthComputable ? a.total : void 0,
                  u = r - o,
                  c = l(u),
                  d = r <= i;
              o = r;
              const f = {
                  loaded: r,
                  total: i,
                  progress: i ? r / i : void 0,
                  bytes: u,
                  rate: c || void 0,
                  estimated: c && i && d ? (i - r) / c : void 0,
                  event: a,
                  lengthComputable: i != null,
                  [t ? "download" : "upload"]: !0
              };
              e(f)
          }, n)
      },
      Xy = (e, t) => {
          const n = e != null;
          return [o => t[0]({
              lengthComputable: n,
              total: e,
              loaded: o
          }), t[1]]
      },
      Jy = e => (...t) => Me.asap(() => e(...t)),
      mZ = Dn.hasStandardBrowserEnv ? ((e, t) => n => (n = new URL(n, Dn.origin), e.protocol === n.protocol && e.host === n.host && (t || e.port === n.port)))(new URL(Dn.origin), Dn.navigator && /(msie|trident)/i.test(Dn.navigator.userAgent)) : () => !0,
      gZ = Dn.hasStandardBrowserEnv ? {
          write(e, t, n, o, l, a) {
              const r = [e + "=" + encodeURIComponent(t)];
              Me.isNumber(n) && r.push("expires=" + new Date(n).toGMTString()), Me.isString(o) && r.push("path=" + o), Me.isString(l) && r.push("domain=" + l), a === !0 && r.push("secure"), document.cookie = r.join("; ")
          },
          read(e) {
              const t = document.cookie.match(new RegExp("(^|;\\s*)(" + e + ")=([^;]*)"));
              return t ? decodeURIComponent(t[3]) : null
          },
          remove(e) {
              this.write(e, "", Date.now() - 864e5)
          }
      } : {
          write() {},
          read() {
              return null
          },
          remove() {}
      };

  function bZ(e) {
      return /^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)
  }

  function yZ(e, t) {
      return t ? e.replace(/\/?\/$/, "") + "/" + t.replace(/^\/+/, "") : e
  }

  function u2(e, t, n) {
      let o = !bZ(t);
      return e && (o || n == !1) ? yZ(e, t) : t
  }
  const Zy = e => e instanceof so ? {
      ...e
  } : e;

  function lr(e, t) {
      t = t || {};
      const n = {};

      function o(c, d, f, h) {
          return Me.isPlainObject(c) && Me.isPlainObject(d) ? Me.merge.call({
              caseless: h
          }, c, d) : Me.isPlainObject(d) ? Me.merge({}, d) : Me.isArray(d) ? d.slice() : d
      }

      function l(c, d, f, h) {
          if (Me.isUndefined(d)) {
              if (!Me.isUndefined(c)) return o(void 0, c, f, h)
          } else return o(c, d, f, h)
      }

      function a(c, d) {
          if (!Me.isUndefined(d)) return o(void 0, d)
      }

      function r(c, d) {
          if (Me.isUndefined(d)) {
              if (!Me.isUndefined(c)) return o(void 0, c)
          } else return o(void 0, d)
      }

      function i(c, d, f) {
          if (f in t) return o(c, d);
          if (f in e) return o(void 0, c)
      }
      const u = {
          url: a,
          method: a,
          data: a,
          baseURL: r,
          transformRequest: r,
          transformResponse: r,
          paramsSerializer: r,
          timeout: r,
          timeoutMessage: r,
          withCredentials: r,
          withXSRFToken: r,
          adapter: r,
          responseType: r,
          xsrfCookieName: r,
          xsrfHeaderName: r,
          onUploadProgress: r,
          onDownloadProgress: r,
          decompress: r,
          maxContentLength: r,
          maxBodyLength: r,
          beforeRedirect: r,
          transport: r,
          httpAgent: r,
          httpsAgent: r,
          cancelToken: r,
          socketPath: r,
          responseEncoding: r,
          validateStatus: i,
          headers: (c, d, f) => l(Zy(c), Zy(d), f, !0)
      };
      return Me.forEach(Object.keys(Object.assign({}, e, t)), function(d) {
          const f = u[d] || l,
              h = f(e[d], t[d], d);
          Me.isUndefined(h) && f !== i || (n[d] = h)
      }), n
  }
  const c2 = e => {
          const t = lr({}, e);
          let {
              data: n,
              withXSRFToken: o,
              xsrfHeaderName: l,
              xsrfCookieName: a,
              headers: r,
              auth: i
          } = t;
          t.headers = r = so.from(r), t.url = l2(u2(t.baseURL, t.url, t.allowAbsoluteUrls), e.params, e.paramsSerializer), i && r.set("Authorization", "Basic " + btoa((i.username || "") + ":" + (i.password ? unescape(encodeURIComponent(i.password)) : "")));
          let u;
          if (Me.isFormData(n)) {
              if (Dn.hasStandardBrowserEnv || Dn.hasStandardBrowserWebWorkerEnv) r.setContentType(void 0);
              else if ((u = r.getContentType()) !== !1) {
                  const [c, ...d] = u ? u.split(";").map(f => f.trim()).filter(Boolean) : [];
                  r.setContentType([c || "multipart/form-data", ...d].join("; "))
              }
          }
          if (Dn.hasStandardBrowserEnv && (o && Me.isFunction(o) && (o = o(t)), o || o !== !1 && mZ(t.url))) {
              const c = l && a && gZ.read(a);
              c && r.set(l, c)
          }
          return t
      },
      wZ = typeof XMLHttpRequest < "u",
      CZ = wZ && function(e) {
          return new Promise(function(n, o) {
              const l = c2(e);
              let a = l.data;
              const r = so.from(l.headers).normalize();
              let {
                  responseType: i,
                  onUploadProgress: u,
                  onDownloadProgress: c
              } = l, d, f, h, p, m;

              function v() {
                  p && p(), m && m(), l.cancelToken && l.cancelToken.unsubscribe(d), l.signal && l.signal.removeEventListener("abort", d)
              }
              let g = new XMLHttpRequest;
              g.open(l.method.toUpperCase(), l.url, !0), g.timeout = l.timeout;

              function b() {
                  if (!g) return;
                  const y = so.from("getAllResponseHeaders" in g && g.getAllResponseHeaders()),
                      w = {
                          data: !i || i === "text" || i === "json" ? g.responseText : g.response,
                          status: g.status,
                          statusText: g.statusText,
                          headers: y,
                          config: e,
                          request: g
                      };
                  i2(function(E) {
                      n(E), v()
                  }, function(E) {
                      o(E), v()
                  }, w), g = null
              }
              "onloadend" in g ? g.onloadend = b : g.onreadystatechange = function() {
                  !g || g.readyState !== 4 || g.status === 0 && !(g.responseURL && g.responseURL.indexOf("file:") === 0) || setTimeout(b)
              }, g.onabort = function() {
                  g && (o(new xt("Request aborted", xt.ECONNABORTED, e, g)), g = null)
              }, g.onerror = function() {
                  o(new xt("Network Error", xt.ERR_NETWORK, e, g)), g = null
              }, g.ontimeout = function() {
                  let C = l.timeout ? "timeout of " + l.timeout + "ms exceeded" : "timeout exceeded";
                  const w = l.transitional || a2;
                  l.timeoutErrorMessage && (C = l.timeoutErrorMessage), o(new xt(C, w.clarifyTimeoutError ? xt.ETIMEDOUT : xt.ECONNABORTED, e, g)), g = null
              }, a === void 0 && r.setContentType(null), "setRequestHeader" in g && Me.forEach(r.toJSON(), function(C, w) {
                  g.setRequestHeader(w, C)
              }), Me.isUndefined(l.withCredentials) || (g.withCredentials = !!l.withCredentials), i && i !== "json" && (g.responseType = l.responseType), c && ([h, m] = Lc(c, !0), g.addEventListener("progress", h)), u && g.upload && ([f, p] = Lc(u), g.upload.addEventListener("progress", f), g.upload.addEventListener("loadend", p)), (l.cancelToken || l.signal) && (d = y => {
                  g && (o(!y || y.type ? new _s(null, e, g) : y), g.abort(), g = null)
              }, l.cancelToken && l.cancelToken.subscribe(d), l.signal && (l.signal.aborted ? d() : l.signal.addEventListener("abort", d)));
              const S = pZ(l.url);
              if (S && Dn.protocols.indexOf(S) === -1) {
                  o(new xt("Unsupported protocol " + S + ":", xt.ERR_BAD_REQUEST, e));
                  return
              }
              g.send(a || null)
          })
      },
      SZ = (e, t) => {
          const {
              length: n
          } = e = e ? e.filter(Boolean) : [];
          if (t || n) {
              let o = new AbortController,
                  l;
              const a = function(c) {
                  if (!l) {
                      l = !0, i();
                      const d = c instanceof Error ? c : this.reason;
                      o.abort(d instanceof xt ? d : new _s(d instanceof Error ? d.message : d))
                  }
              };
              let r = t && setTimeout(() => {
                  r = null, a(new xt(`timeout ${t} of ms exceeded`, xt.ETIMEDOUT))
              }, t);
              const i = () => {
                  e && (r && clearTimeout(r), r = null, e.forEach(c => {
                      c.unsubscribe ? c.unsubscribe(a) : c.removeEventListener("abort", a)
                  }), e = null)
              };
              e.forEach(c => c.addEventListener("abort", a));
              const {
                  signal: u
              } = o;
              return u.unsubscribe = () => Me.asap(i), u
          }
      },
      _Z = function*(e, t) {
          let n = e.byteLength;
          if (n < t) {
              yield e;
              return
          }
          let o = 0,
              l;
          for (; o < n;) l = o + t, yield e.slice(o, l), o = l
      },
      kZ = async function*(e, t) {
          for await (const n of EZ(e)) yield* _Z(n, t)
      }, EZ = async function*(e) {
          if (e[Symbol.asyncIterator]) {
              yield* e;
              return
          }
          const t = e.getReader();
          try {
              for (;;) {
                  const {
                      done: n,
                      value: o
                  } = await t.read();
                  if (n) break;
                  yield o
              }
          } finally {
              await t.cancel()
          }
      }, Qy = (e, t, n, o) => {
          const l = kZ(e, t);
          let a = 0,
              r, i = u => {
                  r || (r = !0, o && o(u))
              };
          return new ReadableStream({
              async pull(u) {
                  try {
                      const {
                          done: c,
                          value: d
                      } = await l.next();
                      if (c) {
                          i(), u.close();
                          return
                      }
                      let f = d.byteLength;
                      if (n) {
                          let h = a += f;
                          n(h)
                      }
                      u.enqueue(new Uint8Array(d))
                  } catch (c) {
                      throw i(c), c
                  }
              },
              cancel(u) {
                  return i(u), l.return()
              }
          }, {
              highWaterMark: 2
          })
      }, Pd = typeof fetch == "function" && typeof Request == "function" && typeof Response == "function", d2 = Pd && typeof ReadableStream == "function", TZ = Pd && (typeof TextEncoder == "function" ? (e => t => e.encode(t))(new TextEncoder) : async e => new Uint8Array(await new Response(e).arrayBuffer())), f2 = (e, ...t) => {
          try {
              return !!e(...t)
          } catch {
              return !1
          }
      }, $Z = d2 && f2(() => {
          let e = !1;
          const t = new Request(Dn.origin, {
              body: new ReadableStream,
              method: "POST",
              get duplex() {
                  return e = !0, "half"
              }
          }).headers.has("Content-Type");
          return e && !t
      }), e0 = 64 * 1024, Zp = d2 && f2(() => Me.isReadableStream(new Response("").body)), Dc = {
          stream: Zp && (e => e.body)
      };
  Pd && (e => {
      ["text", "arrayBuffer", "blob", "formData", "stream"].forEach(t => {
          !Dc[t] && (Dc[t] = Me.isFunction(e[t]) ? n => n[t]() : (n, o) => {
              throw new xt(`Response type '${t}' is not supported`, xt.ERR_NOT_SUPPORT, o)
          })
      })
  })(new Response);
  const OZ = async e => {
      if (e == null) return 0;
      if (Me.isBlob(e)) return e.size;
      if (Me.isSpecCompliantForm(e)) return (await new Request(Dn.origin, {
          method: "POST",
          body: e
      }).arrayBuffer()).byteLength;
      if (Me.isArrayBufferView(e) || Me.isArrayBuffer(e)) return e.byteLength;
      if (Me.isURLSearchParams(e) && (e = e + ""), Me.isString(e)) return (await TZ(e)).byteLength
  }, NZ = async (e, t) => {
      const n = Me.toFiniteNumber(e.getContentLength());
      return n ?? OZ(t)
  }, xZ = Pd && (async e => {
      let {
          url: t,
          method: n,
          data: o,
          signal: l,
          cancelToken: a,
          timeout: r,
          onDownloadProgress: i,
          onUploadProgress: u,
          responseType: c,
          headers: d,
          withCredentials: f = "same-origin",
          fetchOptions: h
      } = c2(e);
      c = c ? (c + "").toLowerCase() : "text";
      let p = SZ([l, a && a.toAbortSignal()], r),
          m;
      const v = p && p.unsubscribe && (() => {
          p.unsubscribe()
      });
      let g;
      try {
          if (u && $Z && n !== "get" && n !== "head" && (g = await NZ(d, o)) !== 0) {
              let w = new Request(t, {
                      method: "POST",
                      body: o,
                      duplex: "half"
                  }),
                  _;
              if (Me.isFormData(o) && (_ = w.headers.get("content-type")) && d.setContentType(_), w.body) {
                  const [E, I] = Xy(g, Lc(Jy(u)));
                  o = Qy(w.body, e0, E, I)
              }
          }
          Me.isString(f) || (f = f ? "include" : "omit");
          const b = "credentials" in Request.prototype;
          m = new Request(t, {
              ...h,
              signal: p,
              method: n.toUpperCase(),
              headers: d.normalize().toJSON(),
              body: o,
              duplex: "half",
              credentials: b ? f : void 0
          });
          let S = await fetch(m, h);
          const y = Zp && (c === "stream" || c === "response");
          if (Zp && (i || y && v)) {
              const w = {};
              ["status", "statusText", "headers"].forEach(N => {
                  w[N] = S[N]
              });
              const _ = Me.toFiniteNumber(S.headers.get("content-length")),
                  [E, I] = i && Xy(_, Lc(Jy(i), !0)) || [];
              S = new Response(Qy(S.body, e0, E, () => {
                  I && I(), v && v()
              }), w)
          }
          c = c || "text";
          let C = await Dc[Me.findKey(Dc, c) || "text"](S, e);
          return !y && v && v(), await new Promise((w, _) => {
              i2(w, _, {
                  data: C,
                  headers: so.from(S.headers),
                  status: S.status,
                  statusText: S.statusText,
                  config: e,
                  request: m
              })
          })
      } catch (b) {
          throw v && v(), b && b.name === "TypeError" && /Load failed|fetch/i.test(b.message) ? Object.assign(new xt("Network Error", xt.ERR_NETWORK, e, m), {
              cause: b.cause || b
          }) : xt.from(b, b && b.code, e, m)
      }
  }), Qp = {
      http: jJ,
      xhr: CZ,
      fetch: xZ
  };
  Me.forEach(Qp, (e, t) => {
      if (e) {
          try {
              Object.defineProperty(e, "name", {
                  value: t
              })
          } catch {}
          Object.defineProperty(e, "adapterName", {
              value: t
          })
      }
  });
  const t0 = e => `- ${e}`,
      IZ = e => Me.isFunction(e) || e === null || e === !1,
      p2 = {
          getAdapter: e => {
              e = Me.isArray(e) ? e : [e];
              const {
                  length: t
              } = e;
              let n, o;
              const l = {};
              for (let a = 0; a < t; a++) {
                  n = e[a];
                  let r;
                  if (o = n, !IZ(n) && (o = Qp[(r = String(n)).toLowerCase()], o === void 0)) throw new xt(`Unknown adapter '${r}'`);
                  if (o) break;
                  l[r || "#" + a] = o
              }
              if (!o) {
                  const a = Object.entries(l).map(([i, u]) => `adapter ${i} ` + (u === !1 ? "is not supported by the environment" : "is not available in the build"));
                  let r = t ? a.length > 1 ? `since :
` + a.map(t0).join(`
`) : " " + t0(a[0]) : "as no adapter specified";
                  throw new xt("There is no suitable adapter to dispatch the request " + r, "ERR_NOT_SUPPORT")
              }
              return o
          },
          adapters: Qp
      };

  function Of(e) {
      if (e.cancelToken && e.cancelToken.throwIfRequested(), e.signal && e.signal.aborted) throw new _s(null, e)
  }

  function n0(e) {
      return Of(e), e.headers = so.from(e.headers), e.data = $f.call(e, e.transformRequest), ["post", "put", "patch"].indexOf(e.method) !== -1 && e.headers.setContentType("application/x-www-form-urlencoded", !1), p2.getAdapter(e.adapter || lu.adapter)(e).then(function(o) {
          return Of(e), o.data = $f.call(e, e.transformResponse, o), o.headers = so.from(o.headers), o
      }, function(o) {
          return s2(o) || (Of(e), o && o.response && (o.response.data = $f.call(e, e.transformResponse, o.response), o.response.headers = so.from(o.response.headers))), Promise.reject(o)
      })
  }
  const v2 = "1.10.0",
      Ad = {};
  ["object", "boolean", "number", "function", "string", "symbol"].forEach((e, t) => {
      Ad[e] = function(o) {
          return typeof o === e || "a" + (t < 1 ? "n " : " ") + e
      }
  });
  const o0 = {};
  Ad.transitional = function(t, n, o) {
      function l(a, r) {
          return "[Axios v" + v2 + "] Transitional option '" + a + "'" + r + (o ? ". " + o : "")
      }
      return (a, r, i) => {
          if (t === !1) throw new xt(l(r, " has been removed" + (n ? " in " + n : "")), xt.ERR_DEPRECATED);
          return n && !o0[r] && (o0[r] = !0, console.warn(l(r, " has been deprecated since v" + n + " and will be removed in the near future"))), t ? t(a, r, i) : !0
      }
  };
  Ad.spelling = function(t) {
      return (n, o) => (console.warn(`${o} is likely a misspelling of ${t}`), !0)
  };

  function MZ(e, t, n) {
      if (typeof e != "object") throw new xt("options must be an object", xt.ERR_BAD_OPTION_VALUE);
      const o = Object.keys(e);
      let l = o.length;
      for (; l-- > 0;) {
          const a = o[l],
              r = t[a];
          if (r) {
              const i = e[a],
                  u = i === void 0 || r(i, a, e);
              if (u !== !0) throw new xt("option " + a + " must be " + u, xt.ERR_BAD_OPTION_VALUE);
              continue
          }
          if (n !== !0) throw new xt("Unknown option " + a, xt.ERR_BAD_OPTION)
      }
  }
  const Ju = {
          assertOptions: MZ,
          validators: Ad
      },
      tl = Ju.validators;
  let Ya = class {
      constructor(t) {
          this.defaults = t || {}, this.interceptors = {
              request: new Yy,
              response: new Yy
          }
      }
      async request(t, n) {
          try {
              return await this._request(t, n)
          } catch (o) {
              if (o instanceof Error) {
                  let l = {};
                  Error.captureStackTrace ? Error.captureStackTrace(l) : l = new Error;
                  const a = l.stack ? l.stack.replace(/^.+\n/, "") : "";
                  try {
                      o.stack ? a && !String(o.stack).endsWith(a.replace(/^.+\n.+\n/, "")) && (o.stack += `
` + a) : o.stack = a
                  } catch {}
              }
              throw o
          }
      }
      _request(t, n) {
          typeof t == "string" ? (n = n || {}, n.url = t) : n = t || {}, n = lr(this.defaults, n);
          const {
              transitional: o,
              paramsSerializer: l,
              headers: a
          } = n;
          o !== void 0 && Ju.assertOptions(o, {
              silentJSONParsing: tl.transitional(tl.boolean),
              forcedJSONParsing: tl.transitional(tl.boolean),
              clarifyTimeoutError: tl.transitional(tl.boolean)
          }, !1), l != null && (Me.isFunction(l) ? n.paramsSerializer = {
              serialize: l
          } : Ju.assertOptions(l, {
              encode: tl.function,
              serialize: tl.function
          }, !0)), n.allowAbsoluteUrls !== void 0 || (this.defaults.allowAbsoluteUrls !== void 0 ? n.allowAbsoluteUrls = this.defaults.allowAbsoluteUrls : n.allowAbsoluteUrls = !0), Ju.assertOptions(n, {
              baseUrl: tl.spelling("baseURL"),
              withXsrfToken: tl.spelling("withXSRFToken")
          }, !0), n.method = (n.method || this.defaults.method || "get").toLowerCase();
          let r = a && Me.merge(a.common, a[n.method]);
          a && Me.forEach(["delete", "get", "head", "post", "put", "patch", "common"], m => {
              delete a[m]
          }), n.headers = so.concat(r, a);
          const i = [];
          let u = !0;
          this.interceptors.request.forEach(function(v) {
              typeof v.runWhen == "function" && v.runWhen(n) === !1 || (u = u && v.synchronous, i.unshift(v.fulfilled, v.rejected))
          });
          const c = [];
          this.interceptors.response.forEach(function(v) {
              c.push(v.fulfilled, v.rejected)
          });
          let d, f = 0,
              h;
          if (!u) {
              const m = [n0.bind(this), void 0];
              for (m.unshift.apply(m, i), m.push.apply(m, c), h = m.length, d = Promise.resolve(n); f < h;) d = d.then(m[f++], m[f++]);
              return d
          }
          h = i.length;
          let p = n;
          for (f = 0; f < h;) {
              const m = i[f++],
                  v = i[f++];
              try {
                  p = m(p)
              } catch (g) {
                  v.call(this, g);
                  break
              }
          }
          try {
              d = n0.call(this, p)
          } catch (m) {
              return Promise.reject(m)
          }
          for (f = 0, h = c.length; f < h;) d = d.then(c[f++], c[f++]);
          return d
      }
      getUri(t) {
          t = lr(this.defaults, t);
          const n = u2(t.baseURL, t.url, t.allowAbsoluteUrls);
          return l2(n, t.params, t.paramsSerializer)
      }
  };
  Me.forEach(["delete", "get", "head", "options"], function(t) {
      Ya.prototype[t] = function(n, o) {
          return this.request(lr(o || {}, {
              method: t,
              url: n,
              data: (o || {}).data
          }))
      }
  });
  Me.forEach(["post", "put", "patch"], function(t) {
      function n(o) {
          return function(a, r, i) {
              return this.request(lr(i || {}, {
                  method: t,
                  headers: o ? {
                      "Content-Type": "multipart/form-data"
                  } : {},
                  url: a,
                  data: r
              }))
          }
      }
      Ya.prototype[t] = n(), Ya.prototype[t + "Form"] = n(!0)
  });
  let RZ = class h2 {
      constructor(t) {
          if (typeof t != "function") throw new TypeError("executor must be a function.");
          let n;
          this.promise = new Promise(function(a) {
              n = a
          });
          const o = this;
          this.promise.then(l => {
              if (!o._listeners) return;
              let a = o._listeners.length;
              for (; a-- > 0;) o._listeners[a](l);
              o._listeners = null
          }), this.promise.then = l => {
              let a;
              const r = new Promise(i => {
                  o.subscribe(i), a = i
              }).then(l);
              return r.cancel = function() {
                  o.unsubscribe(a)
              }, r
          }, t(function(a, r, i) {
              o.reason || (o.reason = new _s(a, r, i), n(o.reason))
          })
      }
      throwIfRequested() {
          if (this.reason) throw this.reason
      }
      subscribe(t) {
          if (this.reason) {
              t(this.reason);
              return
          }
          this._listeners ? this._listeners.push(t) : this._listeners = [t]
      }
      unsubscribe(t) {
          if (!this._listeners) return;
          const n = this._listeners.indexOf(t);
          n !== -1 && this._listeners.splice(n, 1)
      }
      toAbortSignal() {
          const t = new AbortController,
              n = o => {
                  t.abort(o)
              };
          return this.subscribe(n), t.signal.unsubscribe = () => this.unsubscribe(n), t.signal
      }
      static source() {
          let t;
          return {
              token: new h2(function(l) {
                  t = l
              }),
              cancel: t
          }
      }
  };

  function PZ(e) {
      return function(n) {
          return e.apply(null, n)
      }
  }

  function AZ(e) {
      return Me.isObject(e) && e.isAxiosError === !0
  }
  const ev = {
      Continue: 100,
      SwitchingProtocols: 101,
      Processing: 102,
      EarlyHints: 103,
      Ok: 200,
      Created: 201,
      Accepted: 202,
      NonAuthoritativeInformation: 203,
      NoContent: 204,
      ResetContent: 205,
      PartialContent: 206,
      MultiStatus: 207,
      AlreadyReported: 208,
      ImUsed: 226,
      MultipleChoices: 300,
      MovedPermanently: 301,
      Found: 302,
      SeeOther: 303,
      NotModified: 304,
      UseProxy: 305,
      Unused: 306,
      TemporaryRedirect: 307,
      PermanentRedirect: 308,
      BadRequest: 400,
      Unauthorized: 401,
      PaymentRequired: 402,
      Forbidden: 403,
      NotFound: 404,
      MethodNotAllowed: 405,
      NotAcceptable: 406,
      ProxyAuthenticationRequired: 407,
      RequestTimeout: 408,
      Conflict: 409,
      Gone: 410,
      LengthRequired: 411,
      PreconditionFailed: 412,
      PayloadTooLarge: 413,
      UriTooLong: 414,
      UnsupportedMediaType: 415,
      RangeNotSatisfiable: 416,
      ExpectationFailed: 417,
      ImATeapot: 418,
      MisdirectedRequest: 421,
      UnprocessableEntity: 422,
      Locked: 423,
      FailedDependency: 424,
      TooEarly: 425,
      UpgradeRequired: 426,
      PreconditionRequired: 428,
      TooManyRequests: 429,
      RequestHeaderFieldsTooLarge: 431,
      UnavailableForLegalReasons: 451,
      InternalServerError: 500,
      NotImplemented: 501,
      BadGateway: 502,
      ServiceUnavailable: 503,
      GatewayTimeout: 504,
      HttpVersionNotSupported: 505,
      VariantAlsoNegotiates: 506,
      InsufficientStorage: 507,
      LoopDetected: 508,
      NotExtended: 510,
      NetworkAuthenticationRequired: 511
  };
  Object.entries(ev).forEach(([e, t]) => {
      ev[t] = e
  });

  function m2(e) {
      const t = new Ya(e),
          n = Uk(Ya.prototype.request, t);
      return Me.extend(n, Ya.prototype, t, {
          allOwnKeys: !0
      }), Me.extend(n, t, null, {
          allOwnKeys: !0
      }), n.create = function(l) {
          return m2(lr(e, l))
      }, n
  }
  const fn = m2(lu);
  fn.Axios = Ya;
  fn.CanceledError = _s;
  fn.CancelToken = RZ;
  fn.isCancel = s2;
  fn.VERSION = v2;
  fn.toFormData = Rd;
  fn.AxiosError = xt;
  fn.Cancel = fn.CanceledError;
  fn.all = function(t) {
      return Promise.all(t)
  };
  fn.spread = PZ;
  fn.isAxiosError = AZ;
  fn.mergeConfig = lr;
  fn.AxiosHeaders = so;
  fn.formToJSON = e => r2(Me.isHTMLForm(e) ? new FormData(e) : e);
  fn.getAdapter = p2.getAdapter;
  fn.HttpStatusCode = ev;
  fn.default = fn;
  const {
      Axios: rQ,
      AxiosError: sQ,
      CanceledError: iQ,
      isCancel: uQ,
      CancelToken: cQ,
      VERSION: dQ,
      all: fQ,
      Cancel: pQ,
      isAxiosError: vQ,
      spread: hQ,
      toFormData: mQ,
      AxiosHeaders: gQ,
      HttpStatusCode: bQ,
      formToJSON: yQ,
      getAdapter: wQ,
      mergeConfig: CQ
  } = fn, LZ = {
      class: "tabs-row"
  }, DZ = {
      style: {
          "margin-left": "auto"
      }
  }, VZ = {
      class: "kami-bar"
  }, BZ = {
      style: {
          "text-align": "right"
      }
  }, FZ = {
      key: 0,
      class: "kami-info",
      style: {
          "margin-left": "24px",
          color: "#67C23A"
      }
  }, zZ = {
      style: {
          width: "50%"
      },
      class: "wx-notify-setting-container"
  }, HZ = {
      class: "account-setting-container"
  }, KZ = {
      class: "table-scroll-area"
  }, WZ = {
      class: "bottom-config-row"
  }, jZ = {
      style: {
          "margin-right": "5px"
      }
  }, Nf = "kami_v1", Rs = "kami_expire_v1", l0 = "accountList", UZ = "proxyIpList", qZ = 3, a0 = "wxNotifyConfig", YZ = Y({
      __name: "HelloWorld",
      setup(e) {
          const t = P(""),
              n = P(""),
              o = P(!1),
              l = P(""),
              a = P(!1),
              r = Et({
                  machineId: "1",
                  qqEmail: "",
                  enabled: !1
              }),
              i = {
                  machineId: [{
                      required: !0,
                      message: "机器编号不能为空",
                      trigger: "blur"
                  }],
                  qqEmail: [{
                      required: !0,
                      message: "QQ邮箱不能为空",
                      trigger: "blur"
                  }, {
                      pattern: /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/,
                      message: "请输入有效的邮箱地址",
                      trigger: ["blur", "change"]
                  }]
              },
              u = P(),
              c = async ue => {
                  var G, fe;
                  ue ? (G = u.value) == null || G.validate(he => {
                      var _e;
                      he ? (r.enabled = !0, (_e = window.ipcRenderer) == null || _e.send("setNotifyConfig", {
                          machineId: r.machineId,
                          qqEmail: r.qqEmail,
                          enabled: !0
                      }), Xt.success("通知已开启")) : (r.enabled = !1, Xt.error("请正确填写机器编号和QQ邮箱"))
                  }) : (r.enabled = !1, (fe = window.ipcRenderer) == null || fe.send("setNotifyConfig", {
                      machineId: r.machineId,
                      qqEmail: r.qqEmail,
                      enabled: !1
                  }), Xt.info("通知已关闭"))
              }, d = ["guanzhu", "dianzan", "pinglun", "shoucan"], f = {
                  guanzhu: "关注",
                  dianzan: "点赞",
                  pinglun: "评论",
                  shoucan: "收藏"
              }, h = P(!1);
          atob("5oiR5piv5Zyo5Y2H5LuA5LmI5ZCN5piv77yM5Y+R6YCB5L2c5ZCOdnZ2"), window.ipcRenderer.on("crack-alert", (ue, G) => {
              h.value = !0, D();

              function fe(_e) {
                  const Ce = Uint8Array.from(atob(_e), Ie => Ie.charCodeAt(0));
                  return new TextDecoder().decode(Ce)
              }
              const he = fe(G);
              ei.alert(he, "警告", {
                  confirmButtonText: "知道了",
                  type: "error"
              })
          });

          function p() {
              return {
                  fensiNum: 0,
                  jintianFans: 0,
                  zuotianFans: 0,
                  todayStartFans: 0,
                  guanzhuNum: 0,
                  userName: "",
                  lastResetDate: "",
                  todayTarget: [],
                  isPaused: !1,
                  lastOprateTime: "",
                  tongjiNum: {
                      dianzan: {
                          cur: 0,
                          total: 1500
                      },
                      tuwen: {
                          cur: 0,
                          total: 0
                      },
                      shoucan: {
                          cur: 0,
                          total: 150
                      },
                      pinglun: {
                          cur: 0,
                          total: 180
                      },
                      fenxiang: {
                          cur: 0,
                          total: 0
                      },
                      sixin: {
                          cur: 0,
                          total: 0
                      },
                      guanzhu: {
                          cur: 0,
                          total: 10
                      }
                  }
              }
          }
          const m = ue => window.ipcRenderer.invoke("get-store", ue),
              v = (ue, G) => window.ipcRenderer.invoke("set-store", ue, G),
              g = P([]),
              b = P([]),
              S = P([]),
              y = P("accountSetting"),
              C = P(!1),
              w = Et({
                  id: "",
                  ips: "",
                  startTime: "08:00",
                  endTime: "24:00",
                  runMode: 0
              }),
              _ = P(!1),
              E = Et({
                  id: "",
                  ips: "",
                  index: -1,
                  startTime: "08:00",
                  endTime: "24:00",
                  runMode: 0
              }),
              I = P({
                  guanzhu: 10,
                  dianzan: 1500,
                  pinglun: 180,
                  shoucan: 150
              }),
              N = async () => {
                  const ue = await m(UZ) || [];
                  g.value = [{
                      label: "未代理",
                      value: "未代理",
                      protocol: ""
                  }, ...ue.map(G => ({
                      label: G.name,
                      value: [G.ip, G.port, G.user, G.pass, G.protocol].join("|"),
                      protocol: G.protocol
                  }))], console.log("proxyIpList", g.value)
              }, $ = async () => {
                  const ue = await m(l0) || [];
                  b.value = ue.map((G, fe) => ({
                      ...G,
                      status: "未打开",
                      isPaused: !1,
                      bandDianzanNum: 0,
                      bandShoucanNum: 0,
                      bandPinglunNum: 0,
                      startTime: G.startTime || "08:00",
                      endTime: G.endTime || "24:00",
                      runMode: typeof G.runMode == "number" ? G.runMode : 0
                  })), A()
              }, A = async () => {
                  await v(l0, JSON.parse(JSON.stringify(b.value)))
              }, M = ue => {
                  S.value = ue
              }, j = async () => {
                  S.value.forEach(ue => {
                      d.forEach(G => {
                          ue.options.tongjiNum[G].total = I.value[G], ue.options.tongjiNum[G].cur = 0
                      }), window.ipcRenderer.invoke("update-account-row", {
                          id: ue.id,
                          newRow: {
                              options: JSON.parse(JSON.stringify(ue.options))
                          }
                      }), console.log(ue.options, "pzshu111111111")
                  }), await A(), Xt.success("批量配置成功！")
              }, H = async () => {
                  await N(), w.id = "", w.ips = "未代理", w.startTime = "08:00", w.endTime = "24:00", C.value = !0
              }, L = async () => {
                  if (!w.id || !w.id.trim()) {
                      Xt.warning("请输入抖音id");
                      return
                  }
                  if (!g.value.length) {
                      Xt.error("请先在代理IP配置页面添加代理！");
                      return
                  }
                  const ue = g.value.find(fe => fe.value === w.ips);
                  if (!ue) {
                      Xt.warning("请选择代理IP");
                      return
                  }
                  const G = b.value.length ? Math.max(...b.value.map(fe => fe.index)) + 1 : 1;
                  b.value.push({
                      index: G,
                      id: w.id.trim(),
                      protocol: ue.protocol ?? "",
                      ips: w.ips,
                      status: "未打开",
                      isPaused: !1,
                      bandDianzanNum: 0,
                      bandShoucanNum: 0,
                      bandPinglunNum: 0,
                      options: p(),
                      startTime: w.startTime,
                      endTime: w.endTime,
                      runMode: w.runMode
                  }), await A(), C.value = !1, Xt.success("新增账号成功")
              }, x = async (ue, G) => {
                  await ei.confirm("请确认已关闭该窗口，确定修改该账号?", "修改确认", {
                      type: "warning",
                      confirmButtonText: "修改",
                      cancelButtonText: "取消"
                  }), await N(), E.id = ue.id, E.ips = ue.ips || "未代理", E.index = G, E.startTime = ue.startTime || "08:00", E.endTime = ue.endTime || "24:00", _.value = !0, E.runMode = ue.runMode ?? 0
              }, V = async () => {
                  if (!E.id || !E.id.trim()) {
                      Xt.warning("请输入抖音id");
                      return
                  }
                  const ue = g.value.find(he => he.value === E.ips);
                  if (!ue) {
                      Xt.warning("请选择代理IP");
                      return
                  }
                  const G = E.index;
                  if (G < 0 || G >= b.value.length) {
                      Xt.error("数据异常");
                      return
                  }
                  const fe = b.value[G];
                  fe.id = E.id.trim(), fe.ips = E.ips, fe.protocol = ue.protocol ?? "", fe.startTime = E.startTime, fe.endTime = E.endTime, fe.runMode = E.runMode, await A(), _.value = !1, Xt.success("修改成功")
              }, R = async (ue, G) => {
                  await ei.confirm("请确认已关闭该窗口，确定删除该账号?", "删除确认", {
                      type: "warning",
                      confirmButtonText: "删除",
                      cancelButtonText: "取消"
                  }), b.value.splice(G, 1), b.value.forEach((fe, he) => {
                      fe.index = he + 1
                  }), await A(), window.ipcRenderer.send("deleteAccount", ue.id), Xt.success("删除成功")
              };

          function D() {
              const ue = JSON.parse(JSON.stringify(b.value));
              window.ipcRenderer.send("closeAll", ue)
          }

          function X() {
              const ue = JSON.parse(JSON.stringify(b.value));
              window.ipcRenderer.send("openAll", ue)
          }

          function q() {
              const ue = JSON.parse(JSON.stringify(S.value));
              window.ipcRenderer.send("batchAll", ue)
          }

          function J() {
              const ue = JSON.parse(JSON.stringify(b.value));
              window.ipcRenderer.send("sortAll", ue)
          }

          function Z(ue) {
              ue.status = "未打开", ue.isPaused = !1, ue.bandDianzanNum = 0, ue.bandShoucanNum = 0, ue.bandPinglunNum = 0, A();
              const G = JSON.parse(JSON.stringify(ue));
              console.log("clonedRow", G), window.ipcRenderer.send("open-google-window", G)
          }

          function ne(ue) {
              const G = JSON.parse(JSON.stringify(ue)),
                  fe = ue.startTime || "08:00",
                  he = ue.endTime || "24:00";
              if (!se(fe, he)) {
                  Xt.warning("当前不在允许运行时间段，无法操作！");
                  return
              }
              ue.isPaused ? window.ipcRenderer.send("resume-google-window", G) : window.ipcRenderer.send("stop-google-window", G), ue.isPaused = !ue.isPaused, A()
          }

          function se(ue, G, fe = new Date) {
              const [he, _e] = ue.split(":").map(Number);
              let [Ce, Ie] = G.split(":").map(Number);
              Ce === 24 && (Ce = 0);
              const xe = fe.getHours() * 60 + fe.getMinutes(),
                  me = he * 60 + _e,
                  Pe = Ce * 60 + Ie;
              return me < Pe ? xe >= me && xe < Pe : xe >= me || xe < Pe
          }

          function de(ue) {
              return ue ? new Date(Number(ue)).toLocaleString() : ""
          }
          async function re() {
              var ue;
              if (!t.value.trim()) {
                  Xt.warning("请输入卡密");
                  return
              }
              a.value = !0;
              try {
                  const G = await fn.post("http://***************/pc/active.php", {
                          licenseKey: t.value.trim(),
                          deviceId: n.value || void 0,
                          platform: "pc"
                      }),
                      fe = G.data;
                  fe.errMsg ? (Xt.error(((ue = G.data) == null ? void 0 : ue.msg) || "卡密无效"), o.value = !1, l.value = "", localStorage.removeItem(Nf), localStorage.removeItem(Rs)) : (Xt.success("启动成功"), n.value = fe.data.deviceId, l.value = de(fe.data.到期时间), await v(Nf, t.value.trim()), await v(Rs, l.value), o.value = !0, pe())
              } catch {
                  Xt.error("网络错误，验证失败")
              }
              a.value = !1
          }
          let ce = null;
          ar(() => {
              K()
          });
          let Te = 0;
          async function Le() {
              console.log("心跳检查卡密"), n.value = await window.ipcRenderer.invoke("get-device-id");
              try {
                  const G = (await fn.post("http://***************/pc/check.php", {
                          licenseKey: t.value.trim(),
                          deviceId: n.value,
                          platform: "pc"
                      })).data,
                      fe = G.errCode;
                  console.log(G, "datadatadata"), fe == "lisenceKey-error" ? (Te++, console.warn(`心跳失败 ${Te} 次:`, G.errMsg), Te >= qZ && (o.value = !1, l.value = "", localStorage.removeItem(Rs), D(), Xt.error("卡密失效，已连续3次心跳失败" + G.requestId), Te = 0)) : (console.log("心跳成功:", G), l.value = de(G.data.到期时间), localStorage.setItem(Rs, l.value))
              } catch (ue) {
                  console.error("心跳请求异常", ue)
              }
          }

          function le() {
              K(), Le(), ce = setInterval(Le, 10 * 1e3)
          }

          function K() {
              ce && clearInterval(ce)
          }
          const te = Array.from({
              length: 24
          }, (ue, G) => ({
              label: `${G.toString().padStart(2,"0")}:00`,
              value: `${G.toString().padStart(2,"0")}:00`
          }));
          async function pe() {
              window.ipcRenderer.send("set-only-baidu-proxy", !1), await N(), await $(), window.ipcRenderer.on("account-row-updated", (ue, {
                  id: G,
                  newRow: fe
              }) => {
                  const he = b.value.findIndex(_e => _e.id === G);
                  he !== -1 && (b.value[he] = {
                      ...b.value[he],
                      ...fe
                  }), console.log(b, "upppppppppp")
              })
          }
          rt(async () => {
              n.value = await window.ipcRenderer.invoke("get-device-id");
              const ue = await m(Nf),
                  G = await m(Rs);
              ue && G && (t.value = ue, l.value = G, o.value = !0, pe()), le();
              const fe = await m(a0);
              fe && (r.machineId = fe.machineId || "1", r.qqEmail = fe.qqEmail || "", r.enabled = !!fe.enabled)
          });
          const ye = P(!1);
          return ve(ye, ue => {
              window.ipcRenderer.send("set-only-baidu-proxy", ue)
          }), ve(() => ({
              ...r
          }), async ue => {
              await v(a0, JSON.parse(JSON.stringify(ue)))
          }, {
              deep: !0
          }), (ue, G) => {
              const fe = nt("el-tab-pane"),
                  he = nt("el-tabs"),
                  _e = nt("el-input"),
                  Ce = nt("el-button"),
                  Ie = nt("el-form-item"),
                  xe = nt("el-radio-button"),
                  me = nt("el-radio-group"),
                  Pe = nt("el-form"),
                  tt = nt("el-checkbox"),
                  dt = nt("el-row"),
                  lt = nt("el-table-column"),
                  kt = nt("el-table"),
                  gt = nt("el-input-number"),
                  Ue = nt("el-option"),
                  Xe = nt("el-select"),
                  ct = nt("el-dialog");
              return T(), F(De, null, [z("div", LZ, [B(he, {
                  modelValue: y.value,
                  "onUpdate:modelValue": G[0] || (G[0] = U => y.value = U),
                  stretch: !0,
                  size: "small",
                  type: "card",
                  style: {
                      width: "450px"
                  }
              }, {
                  default: W(() => [B(fe, {
                      label: "账号配置",
                      size: "small",
                      name: "accountSetting"
                  }), B(fe, {
                      label: "代理IP",
                      size: "small",
                      name: "ipSetting"
                  }), B(fe, {
                      label: "微信通知",
                      size: "small",
                      name: "wxNotifySetting"
                  }), B(fe, {
                      label: "运行详情",
                      size: "small",
                      name: "runSetting"
                  })]),
                  _: 1
              }, 8, ["modelValue"]), Ze(z("div", DZ, [z("div", VZ, [B(_e, {
                  modelValue: t.value,
                  "onUpdate:modelValue": G[1] || (G[1] = U => t.value = U),
                  placeholder: "输入卡密",
                  type: "password",
                  style: {
                      width: "180px",
                      "margin-right": "8px"
                  },
                  clearable: "",
                  "show-password": ""
              }, null, 8, ["modelValue"]), B(Ce, {
                  type: "primary",
                  loading: a.value,
                  onClick: re
              }, {
                  default: W(() => [Ye(be(o.value ? "已启动" : "启动"), 1)]),
                  _: 1
              }, 8, ["loading"])]), z("div", BZ, [o.value ? (T(), F("span", FZ, " 有效期：" + be(l.value), 1)) : ae("", !0)])], 512), [
                  [yt, y.value == "accountSetting"]
              ])]), Ze(z("div", null, [B(DX)], 512), [
                  [yt, y.value == "ipSetting"]
              ]), Ze(z("div", null, [B(aJ)], 512), [
                  [yt, y.value == "runSetting"]
              ]), Ze(z("div", zZ, [B(Pe, {
                  model: r,
                  style: {
                      "margin-top": "30px"
                  },
                  rules: i,
                  ref_key: "notifyFormRef",
                  ref: u,
                  "label-width": "80px"
              }, {
                  default: W(() => [B(Ie, {
                      label: "机器编号",
                      prop: "machineId",
                      style: {
                          "margin-bottom": "28px"
                      }
                  }, {
                      default: W(() => [B(_e, {
                          modelValue: r.machineId,
                          "onUpdate:modelValue": G[2] || (G[2] = U => r.machineId = U),
                          placeholder: "请填写机器编号",
                          clearable: ""
                      }, null, 8, ["modelValue"])]),
                      _: 1
                  }), B(Ie, {
                      label: "QQ邮箱",
                      prop: "qqEmail",
                      style: {
                          "margin-bottom": "28px"
                      }
                  }, {
                      default: W(() => [B(_e, {
                          modelValue: r.qqEmail,
                          "onUpdate:modelValue": G[3] || (G[3] = U => r.qqEmail = U),
                          placeholder: "请填写QQ邮箱",
                          clearable: ""
                      }, null, 8, ["modelValue"])]),
                      _: 1
                  }), B(Ie, {
                      label: "开启通知",
                      style: {
                          "margin-bottom": "28px"
                      }
                  }, {
                      default: W(() => [B(me, {
                          modelValue: r.enabled,
                          "onUpdate:modelValue": G[4] || (G[4] = U => r.enabled = U),
                          onChange: c
                      }, {
                          default: W(() => [B(xe, {
                              label: !1
                          }, {
                              default: W(() => G[22] || (G[22] = [Ye("关闭")])),
                              _: 1,
                              __: [22]
                          }), B(xe, {
                              label: !0
                          }, {
                              default: W(() => G[23] || (G[23] = [Ye("开启")])),
                              _: 1,
                              __: [23]
                          })]),
                          _: 1
                      }, 8, ["modelValue"])]),
                      _: 1
                  }), B(Ie, null, {
                      default: W(() => G[24] || (G[24] = [z("span", {
                          style: {
                              color: "#E6A23C"
                          }
                      }, " 上号阶段请勿开启，防止弹窗过多信息轰炸，等上号后半小时再开启最佳 ", -1)])),
                      _: 1,
                      __: [24]
                  })]),
                  _: 1
              }, 8, ["model"])], 512), [
                  [yt, y.value == "wxNotifySetting"]
              ]), Ze(z("div", HZ, [B(dt, {
                  style: {
                      "margin-bottom": "10px"
                  }
              }, {
                  default: W(() => [B(Ce, {
                      type: "primary",
                      disabled: h.value || !o.value,
                      onClick: H
                  }, {
                      default: W(() => G[25] || (G[25] = [Ye("新增抖音号")])),
                      _: 1,
                      __: [25]
                  }, 8, ["disabled"]), B(Ce, {
                      type: "primary",
                      disabled: h.value || !o.value,
                      onClick: X
                  }, {
                      default: W(() => G[26] || (G[26] = [Ye("一键全启")])),
                      _: 1,
                      __: [26]
                  }, 8, ["disabled"]), B(Ce, {
                      disabled: h.value || !o.value,
                      onClick: D
                  }, {
                      default: W(() => G[27] || (G[27] = [Ye("一键关闭")])),
                      _: 1,
                      __: [27]
                  }, 8, ["disabled"]), B(Ce, {
                      disabled: !S.value.length,
                      onClick: q
                  }, {
                      default: W(() => G[28] || (G[28] = [Ye("批量启动")])),
                      _: 1,
                      __: [28]
                  }, 8, ["disabled"]), B(Ce, {
                      disabled: h.value || !o.value,
                      onClick: J
                  }, {
                      default: W(() => G[29] || (G[29] = [Ye("一键排序")])),
                      _: 1,
                      __: [29]
                  }, 8, ["disabled"]), G[31] || (G[31] = z("div", {
                      style: {
                          flex: "1"
                      }
                  }, null, -1)), B(tt, {
                      disabled: h.value || !o.value,
                      modelValue: ye.value,
                      "onUpdate:modelValue": G[5] || (G[5] = U => ye.value = U)
                  }, {
                      default: W(() => G[30] || (G[30] = [Ye("打开百度核对-真实代理IP")])),
                      _: 1,
                      __: [30]
                  }, 8, ["disabled", "modelValue"])]),
                  _: 1,
                  __: [31]
              }), z("div", KZ, [B(kt, {
                  data: b.value,
                  onSelectionChange: M,
                  style: {
                      width: "100%"
                  },
                  border: "",
                  size: "small",
                  height: "100%"
              }, {
                  default: W(() => [B(lt, {
                      type: "selection",
                      width: "35",
                      align: "center"
                  }), B(lt, {
                      prop: "index",
                      label: "窗口",
                      width: "60",
                      align: "center"
                  }), B(lt, {
                      prop: "id",
                      label: "抖音id",
                      align: "center"
                  }, {
                      default: W(({
                          row: U
                      }) => [z("span", null, be(U.id), 1)]),
                      _: 1
                  }), B(lt, {
                      prop: "options",
                      label: "任务：关注|点赞|评论|收藏",
                      width: "200",
                      align: "center"
                  }, {
                      default: W(({
                          row: U
                      }) => {
                          var we, Q, $e;
                          return [z("span", null, be(U.options.tongjiNum.guanzhu.total), 1), G[32] || (G[32] = Ye(" | ")), z("span", null, be(JSON.stringify((we = U.options) == null ? void 0 : we.tongjiNum.dianzan.total)), 1), G[33] || (G[33] = Ye(" | ")), z("span", null, be(JSON.stringify((Q = U.options) == null ? void 0 : Q.tongjiNum.pinglun.total)), 1), G[34] || (G[34] = Ye(" | ")), z("span", null, be(JSON.stringify(($e = U.options) == null ? void 0 : $e.tongjiNum.shoucan.total)), 1)]
                      }),
                      _: 1
                  }), B(lt, {
                      prop: "ips",
                      label: "代理ip",
                      align: "center"
                  }, {
                      default: W(({
                          row: U
                      }) => {
                          var we;
                          return [z("span", null, be(((we = g.value.find(Q => Q.value === U.ips)) == null ? void 0 : we.label) || U.ips), 1)]
                      }),
                      _: 1
                  }), B(lt, {
                      label: "运行时段",
                      width: "200",
                      align: "center"
                  }, {
                      default: W(({
                          row: U
                      }) => [z("div", null, [z("span", null, be(U.startTime || "08:00") + " - " + be(U.endTime || "24:00"), 1)])]),
                      _: 1
                  }), B(lt, {
                      prop: "runMode",
                      label: "运行模式",
                      width: "80",
                      align: "center"
                  }, {
                      default: W(({
                          row: U
                      }) => [z("span", null, be(U.runMode === 1 ? "火力" : "养号"), 1)]),
                      _: 1
                  }), B(lt, {
                      fixed: "right",
                      label: "操作",
                      width: "260",
                      align: "center"
                  }, {
                      default: W(({
                          row: U,
                          $index: we
                      }) => [B(Ce, {
                          size: "small",
                          disabled: h.value || !o.value,
                          onClick: Q => Z(U)
                      }, {
                          default: W(() => G[35] || (G[35] = [Ye("启动")])),
                          _: 2,
                          __: [35]
                      }, 1032, ["disabled", "onClick"]), B(Ce, {
                          size: "small",
                          type: U.isPaused ? "success" : "danger",
                          disabled: h.value || !o.value,
                          onClick: Q => ne(U)
                      }, {
                          default: W(() => [Ye(be(U.isPaused ? "恢复" : "暂停"), 1)]),
                          _: 2
                      }, 1032, ["type", "disabled", "onClick"]), B(Ce, {
                          size: "small",
                          disabled: h.value || !o.value,
                          type: "primary",
                          onClick: () => x(U, we)
                      }, {
                          default: W(() => G[36] || (G[36] = [Ye("修改")])),
                          _: 2,
                          __: [36]
                      }, 1032, ["disabled", "onClick"]), B(Ce, {
                          size: "small",
                          disabled: h.value || !o.value,
                          type: "danger",
                          onClick: () => R(U, we)
                      }, {
                          default: W(() => G[37] || (G[37] = [Ye("删除")])),
                          _: 2,
                          __: [37]
                      }, 1032, ["disabled", "onClick"])]),
                      _: 1
                  })]),
                  _: 1
              }, 8, ["data"])]), z("div", WZ, [B(dt, {
                  style: {
                      "align-items": "center"
                  }
              }, {
                  default: W(() => [G[39] || (G[39] = z("div", {
                      class: "config-title"
                  }, "批量任务配置:", -1)), (T(), F(De, null, vt(d, U => (T(), F(De, {
                      key: U
                  }, [z("span", jZ, be(f[U]), 1), B(gt, {
                      modelValue: I.value[U],
                      "onUpdate:modelValue": we => I.value[U] = we,
                      min: 0,
                      step: 1,
                      controls: !1,
                      style: {
                          width: "90px",
                          "margin-right": "15px"
                      }
                  }, null, 8, ["modelValue", "onUpdate:modelValue"])], 64))), 64)), B(Ce, {
                      type: "primary",
                      disabled: !S.value.length,
                      onClick: j
                  }, {
                      default: W(() => G[38] || (G[38] = [Ye("应用配置")])),
                      _: 1,
                      __: [38]
                  }, 8, ["disabled"]), G[40] || (G[40] = z("span", {
                      class: "hint-text"
                  }, "1.选择账号 → 2.应用配置", -1))]),
                  _: 1,
                  __: [39, 40]
              })]), B(ct, {
                  modelValue: C.value,
                  "onUpdate:modelValue": G[12] || (G[12] = U => C.value = U),
                  title: "新增账号",
                  width: "350px",
                  onClose: G[13] || (G[13] = U => w.id = "")
              }, {
                  footer: W(() => [B(Ce, {
                      onClick: G[11] || (G[11] = U => C.value = !1)
                  }, {
                      default: W(() => G[41] || (G[41] = [Ye("取消")])),
                      _: 1,
                      __: [41]
                  }), B(Ce, {
                      type: "primary",
                      onClick: L
                  }, {
                      default: W(() => G[42] || (G[42] = [Ye("确定")])),
                      _: 1,
                      __: [42]
                  })]),
                  default: W(() => [B(Pe, null, {
                      default: W(() => [B(Ie, {
                          label: "抖音id",
                          required: ""
                      }, {
                          default: W(() => [B(_e, {
                              modelValue: w.id,
                              "onUpdate:modelValue": G[6] || (G[6] = U => w.id = U),
                              placeholder: "输入你的抖音id"
                          }, null, 8, ["modelValue"])]),
                          _: 1
                      }), B(Ie, {
                          label: "代理IP",
                          required: ""
                      }, {
                          default: W(() => [B(Xe, {
                              modelValue: w.ips,
                              "onUpdate:modelValue": G[7] || (G[7] = U => w.ips = U),
                              placeholder: "请选择代理IP",
                              style: {
                                  width: "150px"
                              },
                              clearable: !1,
                              "default-first-option": !0
                          }, {
                              default: W(() => [(T(!0), F(De, null, vt(g.value, U => (T(), ie(Ue, {
                                  key: U.value + U.protocol,
                                  label: U.label,
                                  value: U.value
                              }, null, 8, ["label", "value"]))), 128))]),
                              _: 1
                          }, 8, ["modelValue"])]),
                          _: 1
                      }), B(Ie, {
                          label: "运行模式"
                      }, {
                          default: W(() => [B(Xe, {
                              modelValue: w.runMode,
                              "onUpdate:modelValue": G[8] || (G[8] = U => w.runMode = U),
                              placeholder: "请选择运行模式",
                              style: {
                                  width: "120px"
                              }
                          }, {
                              default: W(() => [B(Ue, {
                                  label: "养号",
                                  value: 0
                              }), B(Ue, {
                                  label: "火力",
                                  value: 1
                              })]),
                              _: 1
                          }, 8, ["modelValue"])]),
                          _: 1
                      }), B(Ie, {
                          label: "开始时间"
                      }, {
                          default: W(() => [B(Xe, {
                              modelValue: w.startTime,
                              "onUpdate:modelValue": G[9] || (G[9] = U => w.startTime = U),
                              placeholder: "请选择开始时间",
                              style: {
                                  width: "120px"
                              }
                          }, {
                              default: W(() => [(T(!0), F(De, null, vt(s(te), U => (T(), ie(Ue, {
                                  key: U.value,
                                  label: U.label,
                                  value: U.value
                              }, null, 8, ["label", "value"]))), 128))]),
                              _: 1
                          }, 8, ["modelValue"])]),
                          _: 1
                      }), B(Ie, {
                          label: "结束时间"
                      }, {
                          default: W(() => [B(Xe, {
                              modelValue: w.endTime,
                              "onUpdate:modelValue": G[10] || (G[10] = U => w.endTime = U),
                              placeholder: "请选择结束时间",
                              style: {
                                  width: "120px"
                              }
                          }, {
                              default: W(() => [(T(!0), F(De, null, vt(s(te), U => (T(), ie(Ue, {
                                  key: U.value,
                                  label: U.label,
                                  value: U.value
                              }, null, 8, ["label", "value"]))), 128))]),
                              _: 1
                          }, 8, ["modelValue"])]),
                          _: 1
                      })]),
                      _: 1
                  })]),
                  _: 1
              }, 8, ["modelValue"]), B(ct, {
                  modelValue: _.value,
                  "onUpdate:modelValue": G[20] || (G[20] = U => _.value = U),
                  title: "修改账号",
                  width: "350px",
                  onClose: G[21] || (G[21] = U => E.id = "")
              }, {
                  footer: W(() => [B(Ce, {
                      onClick: G[19] || (G[19] = U => _.value = !1)
                  }, {
                      default: W(() => G[43] || (G[43] = [Ye("取消")])),
                      _: 1,
                      __: [43]
                  }), B(Ce, {
                      type: "primary",
                      onClick: V
                  }, {
                      default: W(() => G[44] || (G[44] = [Ye("保存")])),
                      _: 1,
                      __: [44]
                  })]),
                  default: W(() => [B(Pe, null, {
                      default: W(() => [B(Ie, {
                          label: "抖音id",
                          required: ""
                      }, {
                          default: W(() => [B(_e, {
                              modelValue: E.id,
                              "onUpdate:modelValue": G[14] || (G[14] = U => E.id = U),
                              placeholder: "输入你的抖音id"
                          }, null, 8, ["modelValue"])]),
                          _: 1
                      }), B(Ie, {
                          label: "代理IP",
                          required: ""
                      }, {
                          default: W(() => [B(Xe, {
                              modelValue: E.ips,
                              "onUpdate:modelValue": G[15] || (G[15] = U => E.ips = U),
                              placeholder: "请选择代理IP",
                              style: {
                                  width: "150px"
                              },
                              clearable: !1,
                              "default-first-option": !0
                          }, {
                              default: W(() => [(T(!0), F(De, null, vt(g.value, U => (T(), ie(Ue, {
                                  key: U.value + U.protocol,
                                  label: U.label,
                                  value: U.value
                              }, null, 8, ["label", "value"]))), 128))]),
                              _: 1
                          }, 8, ["modelValue"])]),
                          _: 1
                      }), B(Ie, {
                          label: "运行模式"
                      }, {
                          default: W(() => [B(Xe, {
                              modelValue: E.runMode,
                              "onUpdate:modelValue": G[16] || (G[16] = U => E.runMode = U),
                              placeholder: "请选择运行模式",
                              style: {
                                  width: "120px"
                              }
                          }, {
                              default: W(() => [B(Ue, {
                                  label: "养号",
                                  value: 0
                              }), B(Ue, {
                                  label: "火力",
                                  value: 1
                              })]),
                              _: 1
                          }, 8, ["modelValue"])]),
                          _: 1
                      }), B(Ie, {
                          label: "开始时间"
                      }, {
                          default: W(() => [B(Xe, {
                              modelValue: E.startTime,
                              "onUpdate:modelValue": G[17] || (G[17] = U => E.startTime = U),
                              placeholder: "请选择开始时间",
                              style: {
                                  width: "120px"
                              }
                          }, {
                              default: W(() => [(T(!0), F(De, null, vt(s(te), U => (T(), ie(Ue, {
                                  key: U.value,
                                  label: U.label,
                                  value: U.value
                              }, null, 8, ["label", "value"]))), 128))]),
                              _: 1
                          }, 8, ["modelValue"])]),
                          _: 1
                      }), B(Ie, {
                          label: "结束时间"
                      }, {
                          default: W(() => [B(Xe, {
                              modelValue: E.endTime,
                              "onUpdate:modelValue": G[18] || (G[18] = U => E.endTime = U),
                              placeholder: "请选择结束时间",
                              style: {
                                  width: "120px"
                              }
                          }, {
                              default: W(() => [(T(!0), F(De, null, vt(s(te), U => (T(), ie(Ue, {
                                  key: U.value,
                                  label: U.label,
                                  value: U.value
                              }, null, 8, ["label", "value"]))), 128))]),
                              _: 1
                          }, 8, ["modelValue"])]),
                          _: 1
                      })]),
                      _: 1
                  })]),
                  _: 1
              }, 8, ["modelValue"])], 512), [
                  [yt, y.value == "accountSetting"]
              ])], 64)
          }
      }
  }), GZ = pm(YZ, [
      ["__scopeId", "data-v-a49fd2f3"]
  ]), XZ = Y({
      __name: "App",
      setup(e) {
          return (t, n) => (T(), ie(GZ, {
              msg: "Electron + Vite + Vue"
          }))
      }
  });
  window.ipcRenderer.on("main-process-message", (e, ...t) => {
      console.log("[Receive Main-process message]:", ...t)
  });
  Iw(XZ).use(AX).mount("#app").$nextTick(() => {
      postMessage({
          payload: "removeLoading"
      }, "*")
  })
