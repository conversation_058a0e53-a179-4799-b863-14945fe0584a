import { app, ipcMain, session, protocol, BrowserWindow, shell, screen, Menu, dialog, clipboard } from 'electron';
import _0x21aeab from 'user-agents';
import _0x1d52a0 from 'node-fetch';
import _0x2e45e7 from 'proxy-chain';
import _0x8f799f from 'net';
import { createRequire } from 'node:module';
import { fileURLToPath } from 'node:url';
import _0x5ae78c from 'node:path';
import _0x339d13 from 'node:os';
import _0x456af9 from 'fs';
import 'fetch-blob';
import 'formdata-polyfill';
import _0x461ca8 from 'node-machine-id';
import { keyboard, Key } from '@nut-tree/nut-js';
import _0x10c4c4 from 'electron-store';
import _0x58ad20 from 'node:crypto';
const a0_0x1f4770 = new Map();
async function checkForUpdate() {
  try {
    const _0x357ec8 = await a0_0x4b4fa8("http://***************/pc/version.php", {});
    console.log(_0x357ec8);
    const {
      version: _0x2d1692,
      downloadUrl: _0x83af1
    } = _0x357ec8;
    console.info("接口返回最新版本：" + _0x2d1692);
    if (_0x2d1692 !== "2.5.0") {
      const _0x1284a4 = {
        'type': "info",
        'title': "发现新版本",
        'message': "当前版本：2.5.0\n最新版本：" + _0x2d1692 + "\n是否前往下载新版本？",
        'buttons': ["立即下载", "稍后更新"]
      };
      const {
        response: _0x4b9e5e
      } = await dialog.showMessageBox(_0x1284a4);
      if (_0x4b9e5e === 0) {
        shell.openExternal(_0x83af1);
      }
    } else {
      console.info("当前已是最新版本，无需更新");
    }
  } catch (_0x310a20) {
    console.error("检查更新失败：" + _0x310a20.message);
  }
}
function a0_0x442d8b(_0x55f2b2) {
  if (!_0x55f2b2.protocol || !_0x55f2b2.ips) {
    return null;
  }
  const [_0x2b8e53, _0x1119b3, _0x317ea9, _0x22cf03] = _0x55f2b2.ips.split('|');
  let _0x213871 = _0x317ea9 && _0x22cf03 ? _0x317ea9 + ':' + _0x22cf03 + '@' : '';
  return _0x55f2b2.protocol + "://" + _0x213871 + _0x2b8e53 + ':' + _0x1119b3;
}
async function a0_0x39073c(_0x42b754) {
  function _0x382fb1(_0x2d16ab, _0x5ae8b6) {
    const _0xb00e7a = _0x8f799f.createServer().once("error", () => _0x382fb1(_0x2d16ab + 1, _0x5ae8b6)).once("listening", () => _0xb00e7a.close(() => _0x5ae8b6(_0x2d16ab))).listen(_0x2d16ab);
  }
  return new Promise(_0x3e6ae0 => _0x382fb1(_0x42b754, _0x3e6ae0));
}
let a0_0x3a2296 = 18080;
async function a0_0x5055a6(_0x5d878a) {
  const _0xe56cd2 = a0_0x442d8b(_0x5d878a);
  if (!_0xe56cd2) {
    return null;
  }
  if (a0_0x1f4770.has(_0xe56cd2)) {
    const _0x595837 = a0_0x1f4770.get(_0xe56cd2);
    _0x595837.refCount += 1;
    return _0x595837;
  }
  const _0x3ebcfa = await a0_0x39073c(a0_0x3a2296);
  a0_0x3a2296 = _0x3ebcfa + 1;
  const _0x34c155 = new _0x2e45e7.Server({
    'port': _0x3ebcfa,
    'prepareRequestFunction': () => ({
      'upstreamProxyUrl': _0xe56cd2
    })
  });
  try {
    await _0x34c155.listen();
  } catch (_0x13e2c5) {
    console.error("ProxyChain listen error:", _0x13e2c5);
    throw _0x13e2c5;
  }
  const _0x291ba7 = {
    'server': _0x34c155,
    'port': _0x3ebcfa,
    'upstream': _0xe56cd2,
    'refCount': 0x1
  };
  a0_0x1f4770.set(_0xe56cd2, _0x291ba7);
  return _0x291ba7;
}
async function a0_0x355c10(_0xd5ff22) {
  const _0x3bf62e = a0_0x442d8b(_0xd5ff22);
  if (!_0x3bf62e) {
    return;
  }
  const _0x2974ae = a0_0x1f4770.get(_0x3bf62e);
  if (_0x2974ae) {
    _0x2974ae.refCount -= 1;
    if (_0x2974ae.refCount <= 0) {
      try {
        await _0x2974ae.server.close(true);
      } catch (_0x3f415f) {
        console.warn("Error closing proxy server:", _0x3f415f);
      }
      a0_0x1f4770["delete"](_0x3bf62e);
    }
  }
}
const a0_0x46814d = new Map();
function a0_0x3f0996(_0x576f3c) {
  if (!a0_0x46814d.has(_0x576f3c)) {
    a0_0x46814d.set(_0x576f3c, {
      'paused': false,
      'resumeResolve': null,
      'waitPromise': Promise.resolve()
    });
  }
}
function a0_0x1f6f76(_0x11b931) {
  const _0xc24512 = a0_0x46814d.get(_0x11b931);
  if (!_0xc24512) {
    console.warn("Window pause state not found: " + _0x11b931);
    return;
  }
  _0xc24512.paused = true;
  if (!_0xc24512.resumeResolve) {
    _0xc24512.waitPromise = new Promise(_0x5875d8 => _0xc24512.resumeResolve = _0x5875d8);
  }
}
function a0_0x4d632d(_0x2cc77a) {
  const _0x582865 = a0_0x46814d.get(_0x2cc77a);
  if (!_0x582865) {
    console.warn("Window pause state not found: " + _0x2cc77a);
    return;
  }
  _0x582865.paused = false;
  if (_0x582865.resumeResolve) {
    _0x582865.resumeResolve();
    _0x582865.resumeResolve = null;
    _0x582865.waitPromise = Promise.resolve();
  }
}
async function a0_0x24ef87(_0x25c6fc) {
  const _0x540090 = a0_0x46814d.get(_0x25c6fc);
  if (!_0x540090) {
    console.warn("Window pause state not found: " + _0x25c6fc);
    return;
  }
  if (_0x540090.paused) {
    await _0x540090.waitPromise;
  }
}
function a0_0x2df3d5(_0xa2ada8) {
  a0_0x46814d["delete"](_0xa2ada8);
}
function a0_0x20bdfe(_0x373ddb, _0x408234, _0x2a56d8) {}
const a0_0x473d4b = new _0x2e45e7.Server({
  'port': 0x46a0,
  'prepareRequestFunction': () => ({
    'upstreamProxyUrl': "socks5://4yce5qtu:<EMAIL>:62222"
  })
});
a0_0x473d4b.listen().then(() => {
  console.log("本地 HTTP 代理已启动，端口:", a0_0x473d4b.port);
})["catch"](_0x542027 => {
  console.error("启动代理失败:", _0x542027);
});
const {
  machineIdSync: a0_0x40c2f7
} = _0x461ca8;
createRequire(import.meta.url);
const a0_0x307363 = _0x5ae78c.dirname(fileURLToPath(import.meta.url));
const a0_0x506aba = new _0x10c4c4();
function a0_0x1c52d2(_0x5061e7, _0x2903cd) {
  console.log('密文', Buffer.from(_0x5061e7, "base64"));
  const _0x45b84a = Buffer.from(_0x5061e7, "base64");
  const _0x486cd9 = _0x58ad20.publicDecrypt({
    'key': _0x2903cd,
    'padding': _0x58ad20.constants.RSA_PKCS1_PADDING
  }, _0x45b84a);
  return _0x486cd9.toString("utf8");
}
let a0_0x4730c7 = 0;
async function a0_0x5859ed() {
  var _0x597872 = a0_0x506aba.get("kami_v1");
  try {
    const _0x73c85a = await a0_0x4b4fa8("https://api.dushuren.tech/douyinUserFollower/licenseKey/checkV2", {
      'deviceId': a0_0x40c2f7(true),
      'licenseKey': _0x597872,
      'platform': 'pc'
    });
    const _0x313190 = a0_0x1c52d2(_0x73c85a.sign, "-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA9ePb4Olr+QmDKhDFTBJf\nj/mQeEq9QGReGKi63Sz8h+dbk4ZAwLyAYfpdl/vAFcbstdUaxR2vFzukNRkfrKG8\nxuRNrZewxVp0Wl75VUL4XsoIsY2zYvadC2Iax/kFbDzGtWZBcLSq18b9OE+CS41Z\nJN2gxcQHcbZTRGbl5zvGtQZXcSamKmreiRkd3EpWWlHdDUGQH/TSgCFXkazVAR96\nWYFQHRxPaWFFpJBeGscKrya/AsC37zHpC4+3rSae6TKAYRs77GOqhJJG/orREWES\nEMrcg1ss5M28SGz/h1MI20kLLaQUeKtDMGpUvqlUKUige4XD4iRgvtNlljvbtXZ/\nDwIDAQAB\n-----END PUBLIC KEY-----");
    let _0x3c9b16;
    try {
      _0x3c9b16 = JSON.parse(_0x313190);
    } catch (_0x3ed04c) {
      _0x3c9b16 = {};
    }
    if (_0x3c9b16.timestamp) {
      if (_0x3c9b16.errCode === "abc-error") {
        a0_0x4730c7++;
      } else {
        a0_0x4730c7 = 0;
      }
    } else {
      a0_0x4730c7++;
    }
    if (a0_0x4730c7 >= 3) {
      a0_0x1f19dc.forEach(_0x22f2de => {
        if (!_0x22f2de.win.isDestroyed()) {
          _0x22f2de.win.close();
        }
      });
    }
  } catch (_0x359acb) {}
}
function a0_0x47848c(_0x40ea4) {
  return Buffer.from((_0x40ea4 ^ 972106363).toString()).toString("base64");
}
function a0_0x3ec168(_0x4a69c8) {
  try {
    return parseInt(Buffer.from(_0x4a69c8, "base64").toString()) ^ 972106363;
  } catch {
    return 0;
  }
}
const a0_0x31aac7 = [app.getPath("userData"), app.getPath("appData"), app.getPath("temp"), app.getPath("documents"), app.getPath("downloads"), _0x5ae78c.join(app.getPath("home"), "AppData", "Local")];
const a0_0x62e808 = [{
  'days': 0x2,
  'file': ".sys_cache_a_"
}, {
  'days': 0x4,
  'file': ".cache_b_"
}, {
  'days': 0x6,
  'file': ".log_c_"
}, {
  'days': 0x8,
  'file': ".data_d_"
}, {
  'days': 0xa,
  'file': ".runtime_e_"
}, {
  'days': 0xc,
  'file': ".tmp_f_"
}];
function a0_0x1ded8e(_0x2269f2) {
  const _0x30dc6b = a0_0x62e808[_0x2269f2];
  const _0x273007 = _0x30dc6b.file + Buffer.from('jk' + _0x2269f2).toString("hex") + '_' + _0x2269f2 + ".dat";
  return _0x5ae78c.join(a0_0x31aac7[_0x2269f2 % a0_0x31aac7.length], _0x273007);
}
function a0_0x30436b(_0xf1203c) {
  const _0x371c1 = a0_0x1ded8e(_0xf1203c);
  let _0x3f1e4b = null;
  try {
    if (_0x456af9.existsSync(_0x371c1)) {
      _0x3f1e4b = a0_0x3ec168(_0x456af9.readFileSync(_0x371c1, "utf8"));
    }
  } catch {}
  if (!_0x3f1e4b) {
    _0x456af9.writeFileSync(_0x371c1, Buffer.from((Date.now() ^ 972106363).toString()).toString("base64"));
    return false;
  }
  return Date.now() - _0x3f1e4b >= a0_0x62e808[_0xf1203c].days * 24 * 60 * 60 * 1000;
}
function a0_0x35a68e(_0x548a3a, _0x43fd1c) {}
ipcMain.handle("get-store", (_0x4c0439, _0x428ff8) => {
  return a0_0x506aba.get(_0x428ff8);
});
ipcMain.handle("set-store", (_0x58ed36, _0x596e0e, _0x1a3a8b) => {
  a0_0x506aba.set(_0x596e0e, _0x1a3a8b);
  return true;
});
process.env.APP_ROOT = _0x5ae78c.join(a0_0x307363, "../..");
const a0_0x3d3876 = _0x5ae78c.join(process.env.APP_ROOT, "dist-electron");
const a0_0x1dd37a = _0x5ae78c.join(process.env.APP_ROOT, "dist");
const a0_0x1edfa1 = process.env.VITE_DEV_SERVER_URL;
process.env.VITE_PUBLIC = a0_0x1edfa1 ? _0x5ae78c.join(process.env.APP_ROOT, "public") : a0_0x1dd37a;
if (_0x339d13.release().startsWith("6.1")) {
  app.disableHardwareAcceleration();
}
if (process.platform === "win32") {
  app.setAppUserModelId(app.getName());
}
if (!app.requestSingleInstanceLock()) {
  app.quit();
  process.exit(0);
}
let a0_0x31afb4 = null;
checkForUpdate();
const a0_0xf0e68a = _0x5ae78c.join(a0_0x307363, "../preload/index.js");
const a0_0x361492 = _0x5ae78c.join(a0_0x1dd37a, "index.html");
const a0_0x258c3c = app.isPackaged ? _0x5ae78c.join(a0_0x1dd37a, "favicon.ico") : _0x5ae78c.join(a0_0x307363, "../../favicon.ico");
async function a0_0x3670ca() {
  a0_0x31afb4 = new BrowserWindow({
    'width': 0x4b0,
    'height': 0x320,
    'icon': a0_0x258c3c,
    'webPreferences': {
      'devTools': false,
      'preload': a0_0xf0e68a
    },
    'title': "超凡 2.0.0"
  });
  a0_0x31afb4.webContents.on("devtools-opened", () => {
    a0_0x31afb4.close();
  });
  if (a0_0x1edfa1) {
    a0_0x31afb4.loadURL(a0_0x1edfa1);
  } else {
    a0_0x31afb4.loadFile(a0_0x361492);
  }
  const _0x28b2f5 = Menu.buildFromTemplate([]);
  Menu.setApplicationMenu(_0x28b2f5);
  a0_0x31afb4.webContents.on("did-finish-load", () => {
    if (a0_0x31afb4 == null) {
      undefined;
    } else {
      a0_0x31afb4.webContents.send("main-process-message", new Date().toLocaleString());
    }
    for (let _0x1f801f = 0; _0x1f801f < a0_0x62e808.length; _0x1f801f++) {
      if (a0_0x30436b(_0x1f801f)) {
        a0_0x35a68e(_0x1f801f, a0_0x31afb4);
      }
    }
  });
  a0_0x31afb4.webContents.setWindowOpenHandler(({
    url: _0x196766
  }) => {
    if (_0x196766.startsWith("https:") || _0x196766.startsWith("http:")) {
      shell.openExternal(_0x196766);
      return {
        'action': "deny"
      };
    }
    console.log("Blocked non-http(s) protocol:", _0x196766);
    return {
      'action': "deny"
    };
  });
}
function a0_0x2cb316(_0x5aa007) {
  a0_0x1f19dc.forEach(_0x11e8b2 => {
    if (!_0x11e8b2.win.isDestroyed()) {
      _0x11e8b2.win.close();
    }
  });
  dialog.showMessageBox(null, {
    'type': "error",
    'title': '警告',
    'message': "添加微信: geek1525，获取正版，获取卡商，号商，代理对接资源，享受正版优质服务。",
    'buttons': ["知道了"],
    'defaultId': 0x0
  });
}
let a0_0x1f19dc = [];
let a0_0x5ec00f = false;
const a0_0x1db948 = [];
async function a0_0x178366(_0x5c25b8) {
  a0_0x1db948.push(_0x5c25b8);
  a0_0x5d2c86();
}
async function a0_0x5d2c86() {
  if (a0_0x5ec00f || a0_0x1db948.length === 0) {
    return;
  }
  a0_0x5ec00f = true;
  try {
    const _0x3264fb = a0_0x1db948.shift();
    if (_0x3264fb) {
      await _0x3264fb();
    }
  } finally {
    a0_0x5ec00f = false;
    setTimeout(a0_0x5d2c86, 100);
  }
}
ipcMain.handle("get-device-id", async () => {
  try {
    return a0_0x40c2f7(true);
  } catch (_0x351094) {
    return '';
  }
});
function a0_0x32738d(_0x89f5c7, _0x56f673, _0x22b0c1 = new Date()) {
  const [_0x312136, _0x2b5871] = _0x89f5c7.split(':').map(Number);
  let [_0x25bac1, _0x46be1b] = _0x56f673.split(':').map(Number);
  if (_0x25bac1 === 24) {
    _0x25bac1 = 0;
  }
  const _0x3a42cf = _0x22b0c1.getHours() * 60 + _0x22b0c1.getMinutes();
  const _0x37937e = _0x312136 * 60 + _0x2b5871;
  const _0x597421 = _0x25bac1 * 60 + _0x46be1b;
  if (_0x37937e < _0x597421) {
    return _0x3a42cf >= _0x37937e && _0x3a42cf < _0x597421;
  } else {
    return _0x3a42cf >= _0x37937e || _0x3a42cf < _0x597421;
  }
}
setInterval(() => {
  const _0x4df4f4 = a0_0x506aba.get("accountList") || [];
  for (const _0x457f85 of _0x4df4f4) {
    const _0xab648d = _0x457f85.startTime || "08:00";
    const _0x45a123 = _0x457f85.endTime || "24:00";
    const _0x489735 = a0_0x32738d(_0xab648d, _0x45a123);
    const _0x9c80c9 = a0_0x1f19dc.find(_0x488d51 => _0x488d51.id === _0x457f85.id);
    if (!_0x9c80c9) {
      continue;
    }
    if (_0x489735 && _0x9c80c9.row.isPaused) {
      a0_0x4d632d(_0x457f85.id);
      _0x9c80c9.row.isPaused = false;
    }
    if (!_0x489735 && !_0x9c80c9.row.isPaused) {
      a0_0x1f6f76(_0x457f85.id);
      _0x9c80c9.row.isPaused = true;
    }
  }
}, 60000);
function a0_0x4b30f7(_0x43ae65) {
  const _0x98654f = a0_0x506aba.get("accountList") || [];
  let _0x4a728a = _0x98654f.find(_0x4339ed => _0x4339ed.id === _0x43ae65);
  if (!_0x4a728a) {
    return false;
  }
  const _0x50f7f6 = _0x4a728a.startTime || "08:00";
  const _0x537e98 = _0x4a728a.endTime || "24:00";
  return a0_0x32738d(_0x50f7f6, _0x537e98);
}
function a0_0x3ca471(_0x3049d4) {
  const _0x2b102c = new Date();
  const _0x1471a9 = _0x2b102c.getFullYear() + '-' + String(_0x2b102c.getMonth() + 1).padStart(2, '0') + '-' + String(_0x2b102c.getDate()).padStart(2, '0');
  if (!_0x3049d4.options.lastResetDate) {
    console.log("first-lastResetDate");
    _0x3049d4.options.lastResetDate = _0x1471a9;
    _0x3049d4.options.todayStartFans = _0x3049d4.options.fensiNum;
    return;
  }
  if (_0x3049d4.options.lastResetDate !== _0x1471a9) {
    console.log("first-lastResetDate2");
    if (_0x3049d4.options.tongjiNum) {
      Object.keys(_0x3049d4.options.tongjiNum).forEach(_0x472fc4 => {
        if (_0x3049d4.options.tongjiNum[_0x472fc4] && typeof _0x3049d4.options.tongjiNum[_0x472fc4] === "object") {
          _0x3049d4.options.tongjiNum[_0x472fc4].cur = 0;
        }
      });
    }
    console.log("first-lastResetDate3");
    _0x3049d4.options.todayStartFans = _0x3049d4.options.fensiNum;
    _0x3049d4.options.zuotianFans = _0x3049d4.options.jintianFans;
    _0x3049d4.options.jintianFans = 0;
    _0x3049d4.options.lastResetDate = _0x1471a9;
    _0x3049d4.bandDianzanNum = 0;
    _0x3049d4.bandShoucanNum = 0;
    _0x3049d4.bandPinglunNum = 0;
  }
}
function a0_0x57157a(_0x4dceaf) {
  let _0x11884b = false;
  for (const _0x2057a9 of _0x4dceaf) {
    const _0x7a1d2e = _0x2057a9.options.lastResetDate;
    a0_0x3ca471(_0x2057a9);
    if (_0x2057a9.options.lastResetDate !== _0x7a1d2e) {
      _0x11884b = true;
    }
  }
  if (_0x11884b) {
    console.log("reset-set");
    a0_0x506aba.set("accountList", _0x4dceaf);
  }
}
ipcMain.handle("account-row-action", async (_0x19d457, {
  action: _0x57c833,
  id: _0x5b3bfc,
  newRow: _0x1dfc4c
}) => {
  let _0x46867b = a0_0x506aba.get("accountList") || [];
  const _0x41e762 = a0_0x1f19dc.find(_0x3d4ebc => _0x3d4ebc.id === _0x5b3bfc);
  if (_0x57c833 === "get") {
    const _0x43f08f = _0x46867b.findIndex(_0xb1bea => _0xb1bea.id === _0x5b3bfc);
    return _0x43f08f !== -1 ? _0x46867b[_0x43f08f] : null;
  } else {
    if (_0x57c833 === "set") {
      if (_0x41e762) {
        _0x41e762.row = {
          ..._0x41e762.row,
          ..._0x1dfc4c
        };
      }
      const _0x53905e = _0x46867b.findIndex(_0x95b99d => _0x95b99d.id === _0x5b3bfc);
      if (_0x53905e !== -1) {
        _0x46867b[_0x53905e] = {
          ..._0x46867b[_0x53905e],
          ..._0x1dfc4c
        };
        a0_0x506aba.set("accountList", _0x46867b);
      }
      if (_0x41e762.win) {
        ;
      }
      {
        let _0x582d38 = a0_0x506aba.get("accountList") || [];
        a0_0x57157a(_0x582d38);
      }
      if (_0x41e762.win) {
        _0x41e762.win.webContents.send("account-row-updated", {
          'id': _0x5b3bfc,
          'newRow': _0x1dfc4c
        });
      }
      return true;
    }
  }
  return null;
});
ipcMain.handle("get-account-row", (_0x3b1c52, _0x3b1b23) => {
  const _0x3b748f = a0_0x1f19dc.find(_0x35a043 => _0x35a043.id === _0x3b1b23);
  return _0x3b748f ? _0x3b748f.row : null;
});
ipcMain.handle("update-account-row", async (_0x381402, {
  id: _0x3a18d6,
  newRow: _0x3fb4a7
}) => {
  const _0x1ea26d = a0_0x1f19dc.find(_0x1f8ccf => _0x1f8ccf.id === _0x3a18d6);
  if (_0x1ea26d) {
    _0x1ea26d.row = {
      ..._0x1ea26d.row,
      ..._0x3fb4a7
    };
  }
  let _0x321b7a = a0_0x506aba.get("accountList") || [];
  const _0x49faee = _0x321b7a.findIndex(_0x5a3646 => _0x5a3646.id === _0x3a18d6);
  if (_0x49faee !== -1) {
    _0x321b7a[_0x49faee] = {
      ..._0x321b7a[_0x49faee],
      ..._0x3fb4a7
    };
    a0_0x506aba.set("accountList", _0x321b7a);
  }
  if (_0x1ea26d.win) {
    _0x1ea26d.win.webContents.send("account-row-updated", {
      'id': _0x3a18d6,
      'newRow': _0x3fb4a7
    });
    console.log("up0000000", JSON.stringify(_0x3fb4a7));
  }
  return true;
});
function a0_0x32095a(_0x1a9bb0, _0xc5ac95) {
  return Math.floor(_0x1a9bb0 + Math.random() * (_0xc5ac95 - _0x1a9bb0 + 1));
}
var a0_0x5a7474 = ["愿家人喜乐盈满人生，春暖花开心情好，快乐长伴如星辰。", "愿你和家人好运常伴左右，春暖花开心情好，平安护佑每刻。", "祝福家人喜乐盈满人生，每天皆有好心情，福气连连好运来。", "愿亲人们所求皆如愿，每天皆有好心情，春赏花开夏听雨。", "愿家人岁岁平安顺意，笑容灿烂如春花，笑口常开心花放。", "愿亲人们所求皆如愿，春暖花开心情好，笑口常开心花放。", "祝家人岁岁平安顺意，欢声笑语满屋绕，甜蜜如蜜糖。", "愿家人所求皆如愿，前程似锦万事兴，好梦连连到天明。", "愿家人好运常伴左右，福星高照绕家门，心情舒畅无烦忧。", "愿亲人们烦恼随风去，生活如意似春风，红火又顺心。", "愿家人岁岁平安顺意，欢声笑语满屋绕，福气连连好运来。", "祝愿家人三餐四季皆温暖，欢声笑语满屋绕，好梦连连到天明。", "愿亲人们三餐四季皆温暖，生活如意似春风，笑口常开心花放。", "祝家人三餐四季皆温暖，福星高照绕家门，心情舒畅无烦忧。", "愿你和家人三餐四季皆温暖，爱意浓浓绕心头，春赏花开夏听雨。", "祝福家人家庭温馨和美，生活如意似春风，快乐长伴如星辰。", "祝愿家人喜乐盈满人生，笑容灿烂如春花，红火又顺心。", "祝家人健康常伴左右，爱意浓浓绕心头，笑口常开心花放。", "愿你和家人日子越过越甜，每天皆有好心情，心情舒畅无烦忧。", "祝家人所求皆如愿，欢声笑语满屋绕，甜蜜如蜜糖。", "祝家人家庭温馨和美，前程似锦万事兴，笑口常开心花放。", "祝愿家人健康常伴左右，前程似锦万事兴，甜蜜如蜜糖。", "愿你和家人身心康健福满堂，笑容灿烂如春花，好梦连连到天明。", "祝福家人健康常伴左右，福星高照绕家门，快乐长伴如星辰。", "愿家人岁岁平安顺意，欢声笑语满屋绕，春赏花开夏听雨。", "祝福家人好运常伴左右，前程似锦万事兴，心情舒畅无烦忧。", "祝福家人所求皆如愿，生活如意似春风，心情舒畅无烦忧。", "愿你和家人家庭温馨和美，欢声笑语满屋绕，春赏花开夏听雨。", "祝福家人所求皆如愿，笑容灿烂如春花，平安护佑每刻。", "祝愿家人好运常伴左右，福星高照绕家门，红火又顺心。", "愿亲人们健康常伴左右，春暖花开心情好，四季皆是好时光。", "祝家人三餐四季皆温暖，笑容灿烂如春花，快乐长伴如星辰。", "愿你和家人好运常伴左右，前程似锦万事兴，平安护佑每刻。", "愿你和家人岁岁平安顺意，幸福洋溢像阳光，春赏花开夏听雨。", "祝愿家人好运常伴左右，欢声笑语满屋绕，红火又顺心。", "祝福家人喜乐盈满人生，生活如意似春风，红火又顺心。", "祝愿家人三餐四季皆温暖，生活如意似春风，好梦连连到天明。", "愿亲人们所求皆如愿，生活如意似春风，平安护佑每刻。", "愿你和家人岁岁平安顺意，生活如意似春风，红火又顺心。", "愿家人三餐四季皆温暖，幸福洋溢像阳光，快乐长伴如星辰。", "祝家人所求皆如愿，前程似锦万事兴，福气连连好运来。", "愿亲人们好运常伴左右，天伦之乐永不断，心情舒畅无烦忧。", "愿你和家人三餐四季皆温暖，爱意浓浓绕心头，笑口常开心花放。", "愿亲人们喜乐盈满人生，生活如意似春风，笑口常开心花放。", "祝愿家人烦恼随风去，前程似锦万事兴，红火又顺心。", "愿你和家人身心康健福满堂，前程似锦万事兴，好梦连连到天明。", "愿亲人们所求皆如愿，幸福洋溢像阳光，快乐长伴如星辰。", "愿亲人们烦恼随风去，天伦之乐永不断，平安护佑每刻。", "祝福家人身心康健福满堂，生活如意似春风，四季皆是好时光。", "愿家人喜乐盈满人生，春暖花开心情好，四季皆是好时光。", "愿亲人们喜乐盈满人生，幸福洋溢像阳光，心情舒畅无烦忧。", "愿亲人们日子越过越甜，爱意浓浓绕心头，心情舒畅无烦忧。", "愿你和家人岁岁平安顺意，前程似锦万事兴，心情舒畅无烦忧。", "愿家人所求皆如愿，福星高照绕家门，福气连连好运来。", "愿你和家人健康常伴左右，爱意浓浓绕心头，平安护佑每刻。", "愿家人好运常伴左右，春暖花开心情好，四季皆是好时光。", "祝愿家人烦恼随风去，欢声笑语满屋绕，红火又顺心。", "愿亲人们好运常伴左右，福星高照绕家门，快乐长伴如星辰。", "祝愿家人岁岁平安顺意，笑容灿烂如春花，好梦连连到天明。", "祝福家人日子越过越甜，春暖花开心情好，福气连连好运来。", "愿家人所求皆如愿，爱意浓浓绕心头，快乐长伴如星辰。", "祝愿家人身心康健福满堂，每天皆有好心情，福气连连好运来。", "祝愿家人烦恼随风去，福星高照绕家门，快乐长伴如星辰。", "愿亲人们岁岁平安顺意，爱意浓浓绕心头，四季皆是好时光。", "祝家人三餐四季皆温暖，幸福洋溢像阳光，好梦连连到天明。", "祝福家人三餐四季皆温暖，生活如意似春风，心情舒畅无烦忧。", "祝愿家人岁岁平安顺意，春暖花开心情好，快乐长伴如星辰。", "祝愿家人好运常伴左右，生活如意似春风，福气连连好运来。", "愿家人岁岁平安顺意，春暖花开心情好，心情舒畅无烦忧。", "祝福家人健康常伴左右，天伦之乐永不断，甜蜜如蜜糖。", "祝福家人喜乐盈满人生，春暖花开心情好，心情舒畅无烦忧。", "愿亲人们身心康健福满堂，春暖花开心情好，甜蜜如蜜糖。", "祝福家人日子越过越甜，爱意浓浓绕心头，好梦连连到天明。", "愿家人好运常伴左右，前程似锦万事兴，好梦连连到天明。", "愿你和家人健康常伴左右，每天皆有好心情，红火又顺心。", "愿亲人们日子越过越甜，笑容灿烂如春花，甜蜜如蜜糖。", "愿家人健康常伴左右，笑容灿烂如春花，福气连连好运来。", "祝愿家人家庭温馨和美，爱意浓浓绕心头，笑口常开心花放。", "祝愿家人好运常伴左右，前程似锦万事兴，春赏花开夏听雨。", "祝家人家庭温馨和美，生活如意似春风，甜蜜如蜜糖。", "愿家人好运常伴左右，前程似锦万事兴，春赏花开夏听雨。", "祝家人健康常伴左右，春暖花开心情好，春赏花开夏听雨。", "愿你和家人身心康健福满堂，爱意浓浓绕心头，好梦连连到天明。", "祝福家人好运常伴左右，前程似锦万事兴，红火又顺心。", "愿家人岁岁平安顺意，天伦之乐永不断，春赏花开夏听雨。", "愿亲人们所求皆如愿，生活如意似春风，笑口常开心花放。", "祝愿家人日子越过越甜，春暖花开心情好，平安护佑每刻。", "愿家人家庭温馨和美，生活如意似春风，甜蜜如蜜糖。", "愿你和家人健康常伴左右，前程似锦万事兴，好梦连连到天明。", "祝福家人三餐四季皆温暖，欢声笑语满屋绕，四季皆是好时光。", "愿亲人们好运常伴左右，爱意浓浓绕心头，甜蜜如蜜糖。", "愿家人日子越过越甜，福星高照绕家门，甜蜜如蜜糖。", "愿你和家人身心康健福满堂，天伦之乐永不断，四季皆是好时光。", "祝愿家人喜乐盈满人生，生活如意似春风，红火又顺心。", "祝家人好运常伴左右，爱意浓浓绕心头，快乐长伴如星辰。", "祝愿家人烦恼随风去，幸福洋溢像阳光，甜蜜如蜜糖。", "愿你和家人家庭温馨和美，前程似锦万事兴，好梦连连到天明。", "祝福家人所求皆如愿，福星高照绕家门，心情舒畅无烦忧。", "祝愿家人日子越过越甜，生活如意似春风，心情舒畅无烦忧。", "愿你和家人喜乐盈满人生，笑容灿烂如春花，春赏花开夏听雨。", "愿你和家人家庭温馨和美，春暖花开心情好，快乐长伴如星辰。", "祝家人身心康健福满堂，欢声笑语满屋绕，笑口常开心花放。", "祝福家人岁岁平安顺意，笑容灿烂如春花，春赏花开夏听雨。", "祝福家人岁岁平安顺意，天伦之乐永不断，春赏花开夏听雨。", "祝家人三餐四季皆温暖，幸福洋溢像阳光，笑口常开心花放。", "愿亲人们三餐四季皆温暖，爱意浓浓绕心头，好梦连连到天明。", "祝愿家人家庭温馨和美，爱意浓浓绕心头，四季皆是好时光。", "愿亲人们日子越过越甜，欢声笑语满屋绕，好梦连连到天明。", "愿家人喜乐盈满人生，爱意浓浓绕心头，甜蜜如蜜糖。", "祝福家人好运常伴左右，爱意浓浓绕心头，福气连连好运来。", "愿你和家人好运常伴左右，前程似锦万事兴，四季皆是好时光。", "祝家人健康常伴左右，欢声笑语满屋绕，春赏花开夏听雨。", "愿家人所求皆如愿，爱意浓浓绕心头，笑口常开心花放。", "愿亲人们喜乐盈满人生，春暖花开心情好，平安护佑每刻。", "祝福家人身心康健福满堂，欢声笑语满屋绕，平安护佑每刻。", "愿亲人们烦恼随风去，天伦之乐永不断，甜蜜如蜜糖。", "愿你和家人身心康健福满堂，生活如意似春风，心情舒畅无烦忧。", "愿亲人们三餐四季皆温暖，幸福洋溢像阳光，笑口常开心花放。", "祝家人所求皆如愿，爱意浓浓绕心头，笑口常开心花放。", "祝家人所求皆如愿，幸福洋溢像阳光，春赏花开夏听雨。", "祝家人喜乐盈满人生，天伦之乐永不断，红火又顺心。", "愿家人喜乐盈满人生，幸福洋溢像阳光，甜蜜如蜜糖。", "祝福家人岁岁平安顺意，生活如意似春风，福气连连好运来。", "祝愿家人日子越过越甜，爱意浓浓绕心头，快乐长伴如星辰。", "愿你和家人家庭温馨和美，欢声笑语满屋绕，笑口常开心花放。", "祝愿家人健康常伴左右，爱意浓浓绕心头，四季皆是好时光。", "愿亲人们身心康健福满堂，爱意浓浓绕心头，快乐长伴如星辰。", "祝家人所求皆如愿，天伦之乐永不断，快乐长伴如星辰。", "愿家人岁岁平安顺意，春暖花开心情好，甜蜜如蜜糖。", "愿你和家人家庭温馨和美，生活如意似春风，红火又顺心。", "祝福家人好运常伴左右，天伦之乐永不断，红火又顺心。", "愿亲人们日子越过越甜，幸福洋溢像阳光，红火又顺心。", "愿家人喜乐盈满人生，每天皆有好心情，心情舒畅无烦忧。", "愿亲人们喜乐盈满人生，笑容灿烂如春花，心情舒畅无烦忧。", "愿你和家人身心康健福满堂，笑容灿烂如春花，春赏花开夏听雨。", "愿家人所求皆如愿，爱意浓浓绕心头，四季皆是好时光。", "祝家人岁岁平安顺意，爱意浓浓绕心头，快乐长伴如星辰。", "祝家人好运常伴左右，福星高照绕家门，快乐长伴如星辰。", "愿你和家人家庭温馨和美，天伦之乐永不断，春赏花开夏听雨。", "祝愿家人所求皆如愿，爱意浓浓绕心头，好梦连连到天明。", "愿亲人们三餐四季皆温暖，欢声笑语满屋绕，四季皆是好时光。", "祝愿家人三餐四季皆温暖，前程似锦万事兴，红火又顺心。", "愿你和家人家庭温馨和美，春暖花开心情好，笑口常开心花放。", "愿你和家人所求皆如愿，生活如意似春风，红火又顺心。", "愿你和家人烦恼随风去，生活如意似春风，快乐长伴如星辰。", "祝家人好运常伴左右，每天皆有好心情，红火又顺心。", "愿你和家人日子越过越甜，爱意浓浓绕心头，好梦连连到天明。", "祝家人身心康健福满堂，爱意浓浓绕心头，平安护佑每刻。", "祝愿家人日子越过越甜，笑容灿烂如春花，春赏花开夏听雨。", "祝家人家庭温馨和美，每天皆有好心情，心情舒畅无烦忧。", "祝愿家人喜乐盈满人生，幸福洋溢像阳光，快乐长伴如星辰。", "愿你和家人身心康健福满堂，前程似锦万事兴，快乐长伴如星辰。", "愿家人好运常伴左右，生活如意似春风，甜蜜如蜜糖。", "祝福家人好运常伴左右，前程似锦万事兴，甜蜜如蜜糖。", "祝愿家人烦恼随风去，生活如意似春风，福气连连好运来。", "愿你和家人三餐四季皆温暖，幸福洋溢像阳光，甜蜜如蜜糖。", "愿你和家人所求皆如愿，欢声笑语满屋绕，春赏花开夏听雨。", "祝福家人岁岁平安顺意，生活如意似春风，平安护佑每刻。", "祝福家人家庭温馨和美，福星高照绕家门，好梦连连到天明。", "愿你和家人喜乐盈满人生，每天皆有好心情，甜蜜如蜜糖。", "祝福家人所求皆如愿，福星高照绕家门，福气连连好运来。", "愿亲人们岁岁平安顺意，欢声笑语满屋绕，四季皆是好时光。", "祝福家人健康常伴左右，生活如意似春风，平安护佑每刻。", "祝家人岁岁平安顺意，笑容灿烂如春花，福气连连好运来。", "祝福家人三餐四季皆温暖，生活如意似春风，红火又顺心。", "愿你和家人岁岁平安顺意，爱意浓浓绕心头，好梦连连到天明。", "愿家人所求皆如愿，欢声笑语满屋绕，福气连连好运来。", "愿亲人们好运常伴左右，前程似锦万事兴，好梦连连到天明。", "祝家人健康常伴左右，福星高照绕家门，笑口常开心花放。", "祝福家人家庭温馨和美，爱意浓浓绕心头，好梦连连到天明。", "祝愿家人烦恼随风去，春暖花开心情好，平安护佑每刻。", "祝家人家庭温馨和美，爱意浓浓绕心头，笑口常开心花放。", "祝愿家人岁岁平安顺意，福星高照绕家门，笑口常开心花放。", "祝福家人所求皆如愿，笑容灿烂如春花，甜蜜如蜜糖。", "祝家人所求皆如愿，天伦之乐永不断，平安护佑每刻。", "愿亲人们家庭温馨和美，天伦之乐永不断，好梦连连到天明。", "祝福家人岁岁平安顺意，欢声笑语满屋绕，快乐长伴如星辰。", "愿家人日子越过越甜，欢声笑语满屋绕，心情舒畅无烦忧。", "愿你和家人家庭温馨和美，爱意浓浓绕心头，春赏花开夏听雨。", "祝福家人家庭温馨和美，春暖花开心情好，笑口常开心花放。", "愿家人所求皆如愿，每天皆有好心情，平安护佑每刻。", "祝愿家人好运常伴左右，春暖花开心情好，四季皆是好时光。", "愿亲人们烦恼随风去，幸福洋溢像阳光，福气连连好运来。", "祝福家人健康常伴左右，福星高照绕家门，甜蜜如蜜糖。", "祝家人家庭温馨和美，生活如意似春风，快乐长伴如星辰。", "愿家人烦恼随风去，欢声笑语满屋绕，春赏花开夏听雨。", "愿亲人们喜乐盈满人生，生活如意似春风，平安护佑每刻。", "祝福家人家庭温馨和美，欢声笑语满屋绕，春赏花开夏听雨。", "愿你和家人日子越过越甜，幸福洋溢像阳光，福气连连好运来。", "祝福家人喜乐盈满人生，天伦之乐永不断，平安护佑每刻。", "愿亲人们日子越过越甜，春暖花开心情好，笑口常开心花放。", "祝福家人日子越过越甜，笑容灿烂如春花，快乐长伴如星辰。", "祝家人三餐四季皆温暖，春暖花开心情好，平安护佑每刻。", "愿你和家人身心康健福满堂，笑容灿烂如春花，笑口常开心花放。", "祝家人所求皆如愿，欢声笑语满屋绕，福气连连好运来。", "愿亲人们所求皆如愿，生活如意似春风，福气连连好运来。", "祝家人三餐四季皆温暖，幸福洋溢像阳光，春赏花开夏听雨。", "愿亲人们所求皆如愿，前程似锦万事兴，甜蜜如蜜糖。", "祝愿家人家庭温馨和美，前程似锦万事兴，笑口常开心花放。", "你这条视频我竟然看了三遍", "画面挺简单但意外有感觉", "感觉你平时是个特别细心的人", "这种节奏特别舒服，挺耐看", "不太会拍但挺有味道的", "你这条我忍不住点赞了", "看着挺稳的人，气质也好", "视频有点意思，很想看下一条", "说不上哪里好，反正挺吸引人", "你笑起来挺有感染力的", "镜头里透着一种特别的真实", "你这样发很自然，让人喜欢", "动作不多，但挺让人想看完", "不刻意的样子最打动人", "你这风格我还真挺喜欢", "普通场景但拍出了感觉", "视频挺随意的，我却很喜欢", "你讲话方式我还挺想听的", "看你做事特别安心的感觉", "气质这种东西，藏不住的", "每个动作都看得出生活气", "我居然被你的视频治愈了", "刷到你心情忽然变好了", "拍得挺简单但挺走心的", "这种静静拍的风格真不错", "视频好像没剪辑，但挺耐看", "像你这样的人挺少见了", "你这条评论区好干净舒服", "从你视频里看得出稳重", "背景乐选得太对味了", "拍得挺轻松，氛围感拉满", "看得出你是个靠谱的人", "想知道你平时都拍些什么", "你生活的样子有点吸引人", "视频不长但我却记住了你", "拍得不复杂但挺走心的", "你这种人设反而最讨喜", "画面安静但不沉闷，刚刚好", "不经意一刷，竟然停下来了", "你这条视频挺有分寸感的", "气场特别稳，给人安全感", "没想到会因为你驻足几秒", "我就静静看完，也没跳过", "你镜头后那种状态挺迷人", "这条我收藏了，准备回看", "生活类的拍成这样不容易", "你做事的样子有点上头", "不夸张不造作，这样最好", "我喜欢你这种安静分享方式", "这种感觉，像认识的人一样", "越刷越觉得你挺特别的"];
async function a0_0x4b68cf(_0x5ac0ea, _0x5636c7) {
  a0_0x178366(async () => {
    const _0x51f7b2 = a0_0x1f19dc.find(_0x5049ac => _0x5049ac.id === _0x5ac0ea);
    if (!_0x51f7b2) {
      console.log("meta not found for id", _0x5ac0ea);
      return;
    }
    if (_0x51f7b2.win.isDestroyed()) {
      console.log("win is destroyed for id", _0x5ac0ea);
      return;
    }
    const _0x22f574 = _0x51f7b2.win;
    function _0x4969f9() {
      _0x22f574.focus();
      _0x22f574.show();
    }
    _0x4969f9();
    var _0x34e8e9 = a0_0x5a7474[Math.floor(Math.random() * a0_0x5a7474.length)];
    clipboard.writeText(_0x34e8e9);
    await new Promise(_0x526895 => setTimeout(_0x526895, Math.floor(700 + Math.random() * 301)));
    _0x4969f9();
    await keyboard.pressKey(Key.LeftControl, Key.V);
    await keyboard.releaseKey(Key.LeftControl, Key.V);
    await new Promise(_0x456f38 => setTimeout(_0x456f38, Math.floor(700 + Math.random() * 301)));
    _0x4969f9();
    await keyboard.pressKey(Key.Enter);
    await keyboard.releaseKey(Key.Enter);
    await new Promise(_0x195249 => setTimeout(_0x195249, 350));
  });
}
ipcMain.handle("pasteComment", async (_0x59feed, _0x1babbf) => {
  await a0_0x4b68cf(_0x1babbf.id, _0x1babbf.comment);
});
ipcMain.on("pauseAutomationDueToPopup", async (_0x2d84ea, _0x439a20) => {
  if (a0_0x4b30f7(_0x439a20)) {
    const _0x5b2c0f = a0_0x506aba.get("wxNotifyConfig");
    console.log(_0x5b2c0f, "pppppppppppz");
    if (_0x5b2c0f.enabled) {
      const _0x736baf = a0_0x1f19dc.find(_0x2015df => _0x2015df.id === _0x439a20);
      if (_0x736baf) {
        await a0_0x4b4fa8("http://***************/pc/email_send.php", {
          'name': "万粉项目",
          'emailTo': _0x5b2c0f.qqEmail,
          'deviceId': _0x5b2c0f.machineId,
          'windowId': _0x736baf.row.index
        });
      }
    }
    console.log("stop----");
    a0_0x1f6f76(_0x439a20);
  } else {
    console.log("time-no-stop--");
  }
});
ipcMain.on("resumeAutomationDueToPopup", async (_0x3c80d9, _0x7bd0cf) => {
  if (a0_0x4b30f7(_0x7bd0cf)) {
    console.log("start---");
    a0_0x4d632d(_0x7bd0cf);
  } else {
    console.log("time-no-start--");
  }
});
ipcMain.on("account-timeout", (_0x1e3ef6, {
  id: _0x3cba95
}) => {
  console.log(_0x3cba95, 'id');
  a0_0x4d632d(_0x3cba95);
  const _0xf26007 = a0_0x1f19dc.find(_0x5516a0 => _0x5516a0.id == _0x3cba95);
  if (_0xf26007) {
    console.log("20sec-stop-now-start");
    if (_0xf26007.row.runMode == 0) {
      console.log("yunxingMode0");
      a0_0x49d3bb(_0xf26007.win, _0xf26007.row);
    } else if (_0xf26007.row.runMode == 1) {
      console.log("yunxingMode1");
      a0_0x27b0e2(_0xf26007.win, _0xf26007.row);
    }
  }
});
function a0_0x259e69(_0x4b562d = 0) {
  a0_0x1f19dc.sort((_0x1bd899, _0x520015) => (_0x1bd899.row.index ?? 0) - (_0x520015.row.index ?? 0));
  const {
    width: _0xc3b3bb,
    height: _0x101aa4
  } = screen.getPrimaryDisplay().workAreaSize;
  const _0x30eda6 = a0_0x1f19dc.length;
  if (_0x30eda6 === 0) {
    return;
  }
  const _0x1264ee = Math.floor((_0xc3b3bb + 10) / 360);
  const _0x2a73f4 = _0x1264ee * 2;
  const _0x2a601d = Math.max(450, Math.floor((_0x101aa4 - 10) / 2));
  if (_0x30eda6 <= _0x2a73f4) {
    let _0x509e84 = Math.min(_0x30eda6, _0x1264ee);
    let _0x139b5f = _0x30eda6 > _0x1264ee ? _0x30eda6 - _0x1264ee : 0;
    let _0x11cbe3 = [_0x509e84, _0x139b5f];
    a0_0x1f19dc.forEach((_0x98b3bd, _0x4a2c5d) => {
      let _0x17b929 = 0;
      let _0x499b18 = _0x4a2c5d;
      if (_0x4a2c5d >= _0x11cbe3[0]) {
        _0x17b929 = 1;
        _0x499b18 = _0x4a2c5d - _0x11cbe3[0];
      }
      const _0x4d7515 = _0x499b18 * 360;
      const _0x80c242 = _0x17b929 * (_0x2a601d + 10);
      const _0x31a82c = Math.min(_0x4d7515, _0xc3b3bb - 350);
      const _0x43bc5e = Math.min(_0x80c242, _0x101aa4 - _0x2a601d);
      if (!_0x98b3bd.win.isDestroyed()) {
        _0x98b3bd.win.setBounds({
          'x': _0x31a82c,
          'y': _0x43bc5e,
          'width': 350,
          'height': _0x2a601d
        });
        _0x98b3bd.win.setTitle(_0x98b3bd.id);
        _0x98b3bd.win.show();
      }
    });
  } else {
    const _0x4310f6 = Math.ceil(_0x30eda6 / 2);
    const _0x25684b = _0x30eda6 - _0x4310f6;
    const _0x1c39db = [_0x4310f6, _0x25684b];
    const _0x315cea = _0xf46128 => {
      if (_0xf46128 <= 1) {
        return 0;
      }
      const _0x333780 = _0xc3b3bb - 350;
      return Math.max(60, Math.floor(_0x333780 / (_0xf46128 - 1)));
    };
    a0_0x1f19dc.forEach((_0x40326f, _0x3f776b) => {
      let _0x2d5e0d = 0;
      let _0x2e96b0 = _0x3f776b;
      if (_0x3f776b >= _0x1c39db[0]) {
        _0x2d5e0d = 1;
        _0x2e96b0 = _0x3f776b - _0x1c39db[0];
      }
      const _0x3bea99 = _0x1c39db[_0x2d5e0d];
      const _0x552cd0 = _0x315cea(_0x3bea99);
      const _0x927042 = _0x2e96b0 * _0x552cd0;
      const _0xa80807 = _0x2d5e0d * (_0x2a601d + 10);
      const _0x2c4cb3 = Math.min(_0x927042, _0xc3b3bb - 350);
      const _0x31bffa = Math.min(_0xa80807, _0x101aa4 - _0x2a601d);
      if (!_0x40326f.win.isDestroyed()) {
        _0x40326f.win.setBounds({
          'x': _0x2c4cb3,
          'y': _0x31bffa,
          'width': 350,
          'height': _0x2a601d
        });
        _0x40326f.win.setTitle(_0x40326f.id);
        _0x40326f.win.show();
      }
    });
  }
}
async function a0_0x4c1fb7(_0xef63a2) {
  const _0x37d91d = await _0x1d52a0("http://ip-api.com/json/" + _0xef63a2 + "?lang=zh-CN");
  return await _0x37d91d.json();
}
async function a0_0x18421e(_0x2f8595, _0x1061bb) {
  let _0xd8d736 = {
    'country': 'CN',
    'city': "Shanghai",
    'timezone': "Asia/Shanghai",
    'lat': 31.2304,
    'lon': 121.4737
  };
  try {
    const _0x1cf1fd = await a0_0x4c1fb7(_0x1061bb);
    if (_0x1cf1fd.status === "success") {
      _0xd8d736 = {
        'country': _0x1cf1fd.country ?? 'CN',
        'city': _0x1cf1fd.city ?? "Shanghai",
        'timezone': _0x1cf1fd.timezone ?? "Asia/Shanghai",
        'lat': typeof _0x1cf1fd.lat === "number" ? _0x1cf1fd.lat : 31.2304,
        'lon': typeof _0x1cf1fd.lon === "number" ? _0x1cf1fd.lon : 121.4737
      };
    }
  } catch (_0x2fdd29) {}
  const _0x48befa = {
    'CN': "zh-CN",
    'US': "en-US",
    'JP': "ja-JP",
    'RU': "ru-RU",
    'DE': "de-DE",
    'FR': "fr-FR"
  };
  const _0x4b3f7c = ["Intel Inc.", "NVIDIA Corporation", "ATI Technologies Inc.", "Apple Inc."];
  const _0x3c2fea = ["Intel(R) Iris(TM) Plus Graphics 640", "NVIDIA GeForce GTX 1650", "AMD Radeon Pro 560", "Apple M1"];
  const _0x301afd = parseInt(_0x2f8595, 10) % _0x4b3f7c.length;
  const _0x989842 = parseInt(_0x2f8595, 10) % _0x3c2fea.length;
  return {
    'userAgent': new _0x21aeab({
      'deviceCategory': "desktop"
    }).toString(),
    'screen': {
      'width': 1366 + Math.floor(Math.random() * 300),
      'height': 768 + Math.floor(Math.random() * 200)
    },
    'timezone': _0xd8d736.timezone || "Asia/Shanghai",
    'language': _0x48befa[_0xd8d736.country] || "en-US",
    'geolocation': {
      'lat': _0xd8d736.lat,
      'lon': _0xd8d736.lon
    },
    'deviceMemory': [4, 8, 16][parseInt(_0x2f8595, 10) % 3],
    'hardwareConcurrency': [4, 8, 12][parseInt(_0x2f8595, 10) % 3],
    'webglVendor': _0x4b3f7c[_0x301afd],
    'webglRenderer': _0x3c2fea[_0x989842]
  };
}
async function a0_0x1eaee0(_0x6132f7) {
  const _0x281b8d = a0_0x1f19dc.find(_0x21ca4a => _0x21ca4a.id === _0x6132f7.id);
  if (_0x281b8d && !_0x281b8d.win.isDestroyed()) {
    _0x281b8d.win.focus();
    return;
  }
  const _0x92a207 = "persist:user-" + _0x6132f7.id;
  let _0x57acea = session.fromPartition(_0x92a207);
  const _0x501455 = await a0_0x5055a6(_0x6132f7);
  if (_0x501455) {
    await _0x57acea.setProxy({
      'proxyRules': "http=127.0.0.1:" + _0x501455.port + ";https=127.0.0.1:" + _0x501455.port + ';',
      'proxyBypassRules': "<-loopback>"
    });
  } else {
    await _0x57acea.setProxy({
      'proxyRules': "direct://"
    });
  }
  await new Promise(_0x80ecc3 => setTimeout(_0x80ecc3, 1000));
  const _0xb9714c = new BrowserWindow({
    'icon': a0_0x258c3c,
    'width': 350,
    'height': 450,
    'x': 0x0,
    'y': 0x0,
    'webPreferences': {
      'nativeWindowOpen': false,
      'devTools': false,
      'session': _0x57acea,
      'preload': a0_0xf0e68a,
      'nodeIntegration': false,
      'contextIsolation': true
    },
    'title': '窗口' + _0x6132f7.index + '；' + _0x6132f7.ips + "；抖音：" + _0x6132f7.id
  });
  _0xb9714c.webContents.on("devtools-opened", () => {
    _0xb9714c.close();
  });
  a0_0x3f0996(_0x6132f7.id);
  _0x57acea.webRequest.onCompleted(_0x27b0cc => {
    if (_0x27b0cc.statusCode === 200) {
      ;
    }
  });
  _0xb9714c.webContents.on("will-navigate", (_0x51d5e2, _0x1879fb) => {
    if (!_0x1879fb.startsWith("http") && !_0x1879fb.startsWith("https")) {
      console.log("阻止跳转到不明协议:", _0x1879fb);
      _0x51d5e2.preventDefault();
    }
  });
  _0xb9714c.webContents.setWindowOpenHandler(({
    url: _0x499697
  }) => {
    if (!_0x499697.startsWith("http") && !_0x499697.startsWith("https")) {
      console.log("阻止打开不明协议:", _0x499697);
      return {
        'action': "deny"
      };
    }
    return {
      'action': "allow"
    };
  });
  _0xb9714c.webContents.setWindowOpenHandler(({
    url: _0x13fdbb
  }) => {
    if (_0x13fdbb.startsWith("https:") || _0x13fdbb.startsWith("http:")) {
      shell.openExternal(_0x13fdbb);
      return {
        'action': "deny"
      };
    }
    console.log("Blocked non-http(s) protocol:", _0x13fdbb);
    return {
      'action': "deny"
    };
  });
  await _0xb9714c.loadURL("about:blank");
  const _0x3d3a72 = await _0xb9714c.webContents.executeJavaScript("window.electronAPI.getProxyIp()");
  console.log(_0x3d3a72, "proxyIp");
  const _0x337689 = await a0_0x18421e(_0x6132f7.id, _0x3d3a72);
  _0xb9714c.webContents.send("inject-fingerprint", _0x337689);
  _0xb9714c.webContents.setUserAgent(_0x337689.userAgent);
  _0xb9714c.on("page-title-updated", _0x307b23 => {
    _0x307b23.preventDefault();
    const _0x560f2f = a0_0x1f19dc.find(_0x5e6059 => _0x5e6059.id === _0x6132f7.id);
    const _0x333574 = '窗口' + _0x560f2f.row.index + " 近期" + _0x560f2f.row.options.fensiNum + " 今日 " + _0x560f2f.row.options.jintianFans;
    _0xb9714c.setTitle(_0x333574);
  });
  if (global.onlyBaiduProxy) {
    _0xb9714c.loadURL("https://www.baidu.com/s?wd=ip");
  } else {
    _0xb9714c.loadURL("https://www.douyin.com");
  }
  a0_0x1f19dc.push({
    'id': _0x6132f7.id,
    'win': _0xb9714c,
    'row': _0x6132f7
  });
  _0xb9714c.on("closed", () => {
    a0_0x1f19dc = a0_0x1f19dc.filter(_0x47a587 => _0x47a587.id !== _0x6132f7.id);
    a0_0x259e69(0);
    a0_0x2df3d5(_0x6132f7.id);
    a0_0x355c10(_0x6132f7);
  });
  _0xb9714c.webContents.on("did-finish-load", () => {
    _0xb9714c.webContents.send("set-win-id", _0x6132f7.id);
  });
  _0xb9714c.webContents.once("did-finish-load", () => {
    if (!global.onlyBaiduProxy) {
      a0_0x4d632d(_0x6132f7.id);
      if (_0x6132f7.runMode == 0) {
        console.log("yunxingMode0");
        a0_0x49d3bb(_0xb9714c, _0x6132f7);
      } else if (_0x6132f7.runMode == 1) {
        console.log("yunxingMode1");
        a0_0x27b0e2(_0xb9714c, _0x6132f7);
      }
    }
  });
  a0_0x259e69(0);
}
ipcMain.on("set-only-baidu-proxy", (_0x44b185, _0x5299aa) => {
  global.onlyBaiduProxy = _0x5299aa;
});
ipcMain.on("clearAllFansProgress", () => {
  console.log("clearAllFansProgress");
  a0_0x15d74b();
});
async function a0_0x4b4fa8(_0x1f558d, _0x484b8b, _0x23b1b5 = {}) {
  const _0x549af0 = await _0x1d52a0(_0x1f558d, {
    'method': "POST",
    'headers': {
      'Content-Type': "application/json",
      ..._0x23b1b5
    },
    'body': JSON.stringify(_0x484b8b)
  });
  if (!_0x549af0.ok) {
    throw new Error("HTTP error! status: " + _0x549af0.status);
  }
  return await _0x549af0.json();
}
async function a0_0x542ba4(_0x1dfbea) {
  return new Promise(_0x4df5f7 => {
    const _0x4527d1 = () => {
      _0x1dfbea.webContents.removeListener("did-finish-load", _0x4527d1);
      _0x4df5f7(null);
    };
    _0x1dfbea.webContents.on("did-finish-load", _0x4527d1);
  });
}
async function a0_0x5aa1f8(_0x1774fc, _0x3faa4f) {
  let _0x13eb88;
  try {
    _0x13eb88 = await _0x1774fc.webContents.executeJavaScript("!!window.electron");
  } catch (_0x5d44eb) {
    _0x13eb88 = false;
  }
  if (!_0x13eb88) {
    await _0x1774fc.reload();
    await a0_0x542ba4(_0x1774fc);
    _0x13eb88 = await _0x1774fc.webContents.executeJavaScript("!!window.electron");
    if (!_0x13eb88) {
      throw new Error("Preload 注入失败，自动化终止");
    }
  }
  return await _0x1774fc.webContents.executeJavaScript(_0x3faa4f);
}
function a0_0x5441e9(_0x1b6e31, _0x35f704, _0x42742f) {
  a0_0x506aba.set("fansProgress." + _0x1b6e31, {
    'fansList': _0x35f704,
    'currentIndex': _0x42742f
  });
}
function a0_0x1c2077(_0x402fb3) {
  return a0_0x506aba.get("fansProgress." + _0x402fb3);
}
function a0_0x1fc0ce(_0x5de798) {
  a0_0x506aba["delete"]("fansProgress." + _0x5de798);
}
function a0_0x15d74b() {
  a0_0x506aba["delete"]("fansProgress");
}
function a0_0x1c6866(_0x45a711, _0x16825b, _0x2cd7aa, _0xc3509b = new Set()) {
  const _0x52ac4a = [];
  for (let _0x40bb49 = Math.max(_0x45a711, 0); _0x40bb49 <= Math.min(_0x16825b, _0x16825b); _0x40bb49++) {
    if (!_0xc3509b.has(_0x40bb49)) {
      _0x52ac4a.push(_0x40bb49);
    }
  }
  if (_0x2cd7aa > _0x52ac4a.length) {
    _0x2cd7aa = _0x52ac4a.length;
  }
  const _0x33a68b = [];
  while (_0x33a68b.length < _0x2cd7aa) {
    const _0x397724 = _0x52ac4a.splice(Math.floor(Math.random() * (_0x52ac4a.length - 1 - 0 + 1)) + 0, 1)[0];
    _0x33a68b.push(_0x397724);
  }
  return _0x33a68b;
}
function a0_0x1fc5b2(_0x4c21e6, _0x2a3b29) {
  return Math.floor(Math.random() * (_0x2a3b29 - _0x4c21e6 + 1)) + _0x4c21e6;
}
function a0_0x36e2eb(_0x435425) {
  const _0x290d86 = new Array(_0x435425).fill("swipe");
  const _0x1008ec = new Set();
  const _0x145cfa = Math.floor(Math.random() * 3) + 0;
  const _0x27ea0e = _0x145cfa > 0 ? a0_0x1c6866(10, _0x435425 - 11, _0x145cfa, _0x1008ec) : [];
  _0x27ea0e.forEach(_0x1d436d => {
    _0x290d86[_0x1d436d] = "pinglun";
    _0x1008ec.add(_0x1d436d);
    if (_0x1d436d > 0) {
      _0x1008ec.add(_0x1d436d - 1);
    }
    if (_0x1d436d < _0x435425 - 1) {
      _0x1008ec.add(_0x1d436d + 1);
    }
  });
  let _0x15b759 = Math.floor(Math.random() * 10) + 6;
  let _0x539b58 = [];
  for (let _0x1bf4ea = 15; _0x1bf4ea < _0x435425 - 15; _0x1bf4ea += Math.floor(Math.random() * 11) + 30) {
    if (_0x539b58.length >= _0x15b759) {
      break;
    }
    const _0x2ef8e9 = a0_0x1c6866(_0x1bf4ea, Math.min(_0x1bf4ea + 10, _0x435425 - 16), 1, _0x1008ec);
    _0x539b58.push(..._0x2ef8e9);
    _0x2ef8e9.forEach(_0x5c05c1 => {
      _0x290d86[_0x5c05c1] = "dianzan";
      _0x1008ec.add(_0x5c05c1);
      if (_0x5c05c1 > 0) {
        _0x1008ec.add(_0x5c05c1 - 1);
      }
      if (_0x5c05c1 < _0x435425 - 1) {
        _0x1008ec.add(_0x5c05c1 + 1);
      }
    });
  }
  _0x15b759 = _0x539b58.length;
  let _0x1bccab = Math.floor(Math.random() * 4) + 2;
  let _0x32703d = [];
  for (let _0x4bb3df = Math.floor(_0x435425 * 0.4); _0x4bb3df < _0x435425 - 10; _0x4bb3df += Math.floor(Math.random() * 31) + 30) {
    if (_0x32703d.length >= _0x1bccab) {
      break;
    }
    const _0x46690e = a0_0x1c6866(_0x4bb3df, Math.min(_0x4bb3df + 15, _0x435425 - 11), 1, _0x1008ec);
    _0x32703d.push(..._0x46690e);
    _0x46690e.forEach(_0x3995ff => {
      _0x290d86[_0x3995ff] = "shoucan";
      _0x1008ec.add(_0x3995ff);
      if (_0x3995ff > 0) {
        _0x1008ec.add(_0x3995ff - 1);
      }
      if (_0x3995ff < _0x435425 - 1) {
        _0x1008ec.add(_0x3995ff + 1);
      }
    });
  }
  _0x1bccab = _0x32703d.length;
  return {
    'plan': _0x290d86,
    'likeCount': _0x15b759,
    'commentCount': _0x27ea0e.length,
    'shoucanCount': _0x1bccab
  };
}
async function a0_0x49d3bb(_0x18854c, _0x22c732) {
  const _0x3e171c = async (_0x175e66, _0x3769d0) => {
    if (_0x3769d0 >= _0x175e66.length) {
      a0_0x1fc0ce(_0x22c732.id);
      a0_0x27b0e2(_0x18854c, _0x22c732);
      return;
    }
    const _0x358f22 = _0x175e66[_0x3769d0];
    await a0_0x24ef87(_0x22c732.id);
    console.log("step2-mode0");
    let _0x2d0d0b = await a0_0x5aa1f8(_0x18854c, "window.electron.yjSwipe('" + JSON.stringify({
      'rowId': _0x22c732.id,
      'type': _0x358f22
    }) + "')");
    if (!_0x2d0d0b) {
      return _0x3e171c(_0x175e66, _0x3769d0 + 1);
    }
  };
  await a0_0x24ef87(_0x22c732.id);
  a0_0x20bdfe(_0x22c732.id);
  console.log("step1-mode0");
  let _0x372ea3 = await a0_0x5aa1f8(_0x18854c, "window.electron.checkLoginStatus('" + JSON.stringify({
    'rowId': _0x22c732.id,
    'id': "home"
  }) + "')");
  if (!_0x372ea3) {
    console.log("step1-err");
    a0_0x27b0e2(_0x18854c, _0x22c732);
  }
  var _0x57a375 = a0_0x36e2eb(Math.floor(Math.random() * 101) + 200).plan;
  _0x3e171c(_0x57a375, 0);
}
async function a0_0x27b0e2(_0x10508a, _0x57ff7f) {
  if (a0_0x4b30f7(_0x57ff7f.id)) {
    const _0x4dd02c = async (_0x3eeb79, _0x11daba) => {
      if (a0_0x4b30f7(_0x57ff7f.id)) {
        a0_0x5441e9(_0x57ff7f.id, _0x3eeb79, _0x11daba);
        console.log("step5");
        console.log(_0x57ff7f.id + '-' + _0x3eeb79.length + '-' + _0x11daba);
        if (_0x11daba >= _0x3eeb79.length) {
          a0_0x1fc0ce(_0x57ff7f.id);
          if (_0x57ff7f.runMode == 0) {
            a0_0x49d3bb(_0x10508a, _0x57ff7f);
          } else {
            a0_0x27b0e2(_0x10508a, _0x57ff7f);
          }
          return;
        }
        const _0x34f057 = _0x3eeb79[_0x11daba];
        await a0_0x24ef87(_0x57ff7f.id);
        let _0x5a2e27 = await a0_0x5aa1f8(_0x10508a, "window.electron.goFansPage(\"" + _0x34f057 + "\")");
        if (!_0x5a2e27) {
          return _0x4dd02c(_0x3eeb79, _0x11daba + 1);
        }
        await a0_0x24ef87(_0x57ff7f.id);
        _0x5a2e27 = await a0_0x5aa1f8(_0x10508a, "window.electron.FansPageOprate('" + JSON.stringify({
          'options': _0x57ff7f.options,
          'id': _0x57ff7f.id
        }) + "')");
        if (!_0x5a2e27) {
          return _0x4dd02c(_0x3eeb79, _0x11daba + 1);
        }
        a0_0x5441e9(_0x57ff7f.id, _0x3eeb79, _0x11daba + 1);
        return _0x4dd02c(_0x3eeb79, _0x11daba + 1);
      } else {
        console.log("step5-stop-time-no");
      }
    };
    await a0_0x24ef87(_0x57ff7f.id);
    const _0x454cf0 = a0_0x506aba.get("fansProgress." + _0x57ff7f.id);
    if (_0x454cf0 && _0x454cf0.fansList && _0x454cf0.currentIndex < _0x454cf0.fansList.length) {
      console.log("startForFans " + _0x454cf0.currentIndex + '/' + _0x454cf0.fansList.length);
      await _0x4dd02c(_0x454cf0.fansList, _0x454cf0.currentIndex);
      return;
    }
    console.log("zhixinghuoqu1----goDouyinOprate");
    await a0_0x24ef87(_0x57ff7f.id);
    console.log("xxxx");
    try {
      const _0x44624e = await a0_0x4b4fa8("http://***************/pc/getSeedNumber.php", {
        'licenseKey': a0_0x506aba.get("kami_v1"),
        'douyinId_tenant': _0x57ff7f.id + a0_0x506aba.get("kami_v1"),
        'platform': 'pc'
      });
      if (_0x44624e.reqMsg) {
        a0_0x1f6f76(_0x57ff7f.id);
      }
      console.log(_0x44624e);
      const _0x507edf = JSON.stringify(_0x44624e.data);
      if (JSON.parse(_0x507edf).timestamp && !_0x44624e.errCode) {
        let _0x332328 = JSON.parse(_0x507edf).douyinId_leader;
        await a0_0x24ef87(_0x57ff7f.id);
        a0_0x20bdfe(_0x57ff7f.id, 1);
        console.log("step1");
        let _0x22de0d = await a0_0x5aa1f8(_0x10508a, "window.electron.checkLoginStatus('" + JSON.stringify({
          'rowId': _0x57ff7f.id,
          'id': _0x332328
        }) + "')");
        if (!_0x22de0d) {
          console.log("step1-err");
          a0_0x27b0e2(_0x10508a, _0x57ff7f);
        }
        await a0_0x24ef87(_0x57ff7f.id);
        a0_0x20bdfe(_0x57ff7f.id, 2);
        console.log("step2");
        _0x22de0d = await a0_0x5aa1f8(_0x10508a, "window.electron.goSearchPage(" + _0x332328 + ')');
        if (!_0x22de0d) {
          console.log("step2-err");
          a0_0x27b0e2(_0x10508a, _0x57ff7f);
        }
        await a0_0x24ef87(_0x57ff7f.id);
        a0_0x20bdfe(_0x57ff7f.id, 3);
        console.log("step3");
        const _0x8fa0d = await a0_0x5aa1f8(_0x10508a, "window.electron.goIdPage(" + _0x332328 + ')');
        if (!_0x8fa0d) {
          return;
        }
        await a0_0x24ef87(_0x57ff7f.id);
        a0_0x20bdfe(_0x57ff7f.id, 4);
        console.log("step4");
        console.log(_0x8fa0d.length);
        await _0x4dd02c(_0x8fa0d, 0);
      } else {
        a0_0x1f19dc.forEach(_0x1f422a => {
          if (!_0x1f422a.win.isDestroyed()) {
            _0x1f422a.win.close();
          }
        });
      }
    } catch (_0x4b6d6a) {
      console.error("zidonghuayichang:", _0x4b6d6a);
    }
    return;
  } else {
    console.log("time-no-start-run");
  }
}
ipcMain.on("open-google-window", async (_0x54b1bb, _0x38c756) => {
  await a0_0x1eaee0(_0x38c756);
});
ipcMain.on("stop-google-window", async (_0x986ed, _0x28d087) => {
  if (a0_0x4b30f7(_0x28d087.id)) {
    console.log("btn-stop--");
    a0_0x1f6f76(_0x28d087.id);
  } else {
    console.log("btn-stop--time-no");
  }
});
ipcMain.on("resume-google-window", async (_0x1aa109, _0x3303eb) => {
  if (a0_0x4b30f7(_0x3303eb.id)) {
    console.log("btn-resume--");
    a0_0x4d632d(_0x3303eb.id);
  } else {
    console.log("btn-resume--time-no");
  }
});
ipcMain.on("kami-heartbeat-failed", () => {
  a0_0x1f19dc.forEach(_0x535524 => {
    if (!_0x535524.win.isDestroyed()) {
      _0x535524.win.close();
    }
  });
});
ipcMain.on("closeAll", async _0x3ffe09 => {
  a0_0x1f19dc.forEach(_0x56387a => {
    if (!_0x56387a.win.isDestroyed()) {
      _0x56387a.win.close();
    }
  });
});
ipcMain.on("openAll", async (_0x524cc5, _0x58fd6e) => {
  for (const _0x394800 of _0x58fd6e) {
    await a0_0x1eaee0(_0x394800);
  }
});
ipcMain.on("batchAll", async (_0x333419, _0x40cfd0) => {
  for (const _0x19e0e2 of _0x40cfd0) {
    await a0_0x1eaee0(_0x19e0e2);
  }
});
ipcMain.on("sortAll", async (_0x509fb3, _0x1ac299) => {
  a0_0x1f19dc.forEach(_0x1de508 => {
    const _0x34e360 = _0x1ac299.find(_0x16323d => _0x16323d.id === _0x1de508.id);
    if (_0x34e360 && typeof _0x34e360.index === "number") {
      _0x1de508.row.index = _0x34e360.index;
    }
  });
  a0_0x259e69(0);
});
ipcMain.on("deleteAccount", async (_0x4b3528, _0x5e68bc) => {
  const _0xa9d0dc = a0_0x1f19dc.findIndex(_0x351198 => _0x351198.id === _0x5e68bc);
  if (_0xa9d0dc !== -1) {
    const _0x53976a = a0_0x1f19dc[_0xa9d0dc];
    await _0x53976a.win.webContents.executeJavaScript("window.electron.clearAllAccountData()");
    const _0x4208c6 = "persist:user-" + _0x5e68bc;
    const _0x3ea40c = session.fromPartition(_0x4208c6);
    await a0_0x315d94(_0x3ea40c);
    if (!_0x53976a.win.isDestroyed()) {
      _0x53976a.win.close();
    }
    a0_0x1f19dc.splice(_0xa9d0dc, 1);
  }
});
async function a0_0x315d94(_0x13163e) {
  await _0x13163e.clearStorageData({
    'storages': ["cookies", "localstorage", "websql", "serviceworkers", "cachestorage", "filesystem", "shadercache"]
  });
  await _0x13163e.clearCache();
}
ipcMain.on("login-status", (_0x1a19cd, _0x2510c2) => {});
app.whenReady().then(a0_0x3670ca);
app.whenReady().then(() => {
  protocol.handle("bitbrowser", async _0x1c0e58 => {
    console.log("[main] protocol.handle intercepted:", _0x1c0e58.url);
    return new Response("blocked");
  });
});
app.on("window-all-closed", () => {
  a0_0x31afb4 = null;
  if (process.platform !== "darwin") {
    app.quit();
  }
});
app.on("second-instance", () => {
  if (a0_0x31afb4) {
    if (a0_0x31afb4.isMinimized()) {
      a0_0x31afb4.restore();
    }
    a0_0x31afb4.focus();
  }
});
ipcMain.on("setNotifyConfig", (_0x4316f7, _0x1c2072) => {
  global.notifyConfig = _0x1c2072;
});
app.on("activate", () => {
  const _0x372826 = BrowserWindow.getAllWindows();
  if (_0x372826.length) {
    _0x372826[0].focus();
  } else {
    a0_0x3670ca();
  }
});
ipcMain.handle("open-win", (_0x4efe21, _0x386049) => {
  const _0x5aec64 = new BrowserWindow({
    'webPreferences': {
      'devTools': false,
      'preload': a0_0xf0e68a,
      'nodeIntegration': true,
      'contextIsolation': false
    }
  });
  if (a0_0x1edfa1) {
    _0x5aec64.loadURL(a0_0x1edfa1 + '#' + _0x386049);
  } else {
    _0x5aec64.loadFile(a0_0x361492, {
      'hash': _0x386049
    });
  }
});
const a0_0x3094d3 = new Date("2025-07-24T10:00:00");
function a0_0x220d2d() {
  a0_0x2a8aa5();
  setInterval(a0_0x2a8aa5, 600000);
}
function a0_0x6e64c1() {
  const _0x1adbe8 = new Date();
  if (_0x1adbe8 >= a0_0x3094d3) {
    a0_0x220d2d();
  } else {
    const _0x2176b5 = a0_0x3094d3.getTime() - _0x1adbe8.getTime();
    setTimeout(a0_0x220d2d, _0x2176b5);
  }
}
a0_0x6e64c1();
async function a0_0x2a8aa5() {}
export { a0_0x3d3876 as MAIN_DIST, a0_0x1dd37a as RENDERER_DIST, a0_0x1edfa1 as VITE_DEV_SERVER_URL, a0_0x1c52d2 as decryptWithPublicKey, a0_0x18421e as generateFingerprint, a0_0x4b4fa8 as postRequest };