/**
 * Electron 预加载脚本 - 抖音自动化工具
 * 完整重构版本，包含所有原始功能
 * 版本: 2.5.0
 */

"use strict";

// ===== 依赖导入 =====
const { ipc<PERSON><PERSON>er, contextBridge } = require("electron");

// ===== 全局变量 =====
let currentWindowId;
let isPopupPaused = false;
let currentUrl = location.href;
let urlChangeTime = Date.now();
let lastOperationTime = Date.now();
let isToastHandled = false;
let isLoginDialogHandled = false;
let isLoginDialogProcessing = false;

// ===== 工具函数 =====

/**
 * DOM 操作工具
 */
const domUtils = {
  append(parent, child) {
    if (!Array.from(parent.children).find(element => element === child)) {
      return parent.appendChild(child);
    }
  },
  remove(parent, child) {
    if (Array.from(parent.children).find(element => element === child)) {
      return parent.removeChild(child);
    }
  }
};

/**
 * 随机延迟函数
 */
const randomDelay = (min, max) => new Promise(resolve => {
  const delay = Math.floor(Math.random() * (max - min + 1)) + min;
  setTimeout(resolve, delay);
});

/**
 * 生成随机数
 */
function randomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 显示提示消息
 */
function showToast(message) {
  let toastElement = document.createElement("div");
  toastElement.innerText = message;
  Object.assign(toastElement.style, {
    position: "fixed",
    top: "20%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    background: "rgba(0,0,0,0.85)",
    color: "#fff",
    padding: "16px 32px",
    borderRadius: "8px",
    fontSize: "20px",
    zIndex: 99999,
    boxShadow: "0 2px 16px rgba(0,0,0,0.2)",
    textAlign: "center",
    pointerEvents: "none"
  });
  
  document.body.appendChild(toastElement);
  setTimeout(() => {
    toastElement.remove();
  }, 2200);
}

/**
 * 点击元素
 */
const clickElement = async (element) => {
  try {
    const clickEvent = new MouseEvent("click", {
      bubbles: true,
      cancelable: true,
      view: window
    });
    element.dispatchEvent(clickEvent);
  } catch (error) {
    console.error("Click element error:", error);
  }
};

// ===== 窗口ID设置 =====

// 监听窗口ID设置
ipcRenderer.on("set-win-id", (_, windowId) => {
  try {
    currentWindowId = windowId;
    sessionStorage.setItem("WIN_ID", windowId);
  } catch (error) {
    console.error("Set window ID error:", error);
  }
});

// ===== IPC 通信桥接 =====

contextBridge.exposeInMainWorld("ipcRenderer", {
  on(...args) {
    const [channel, listener] = args;
    return ipcRenderer.on(channel, (event, ...data) => listener(event, ...data));
  },
  off(...args) {
    const [channel, ...rest] = args;
    return ipcRenderer.off(channel, ...rest);
  },
  send(...args) {
    const [channel, ...data] = args;
    return ipcRenderer.send(channel, ...data);
  },
  invoke(...args) {
    const [channel, ...data] = args;
    return ipcRenderer.invoke(channel, ...data);
  }
});

// ===== 核心业务功能 =====

/**
 * 清除所有账号数据
 */
async function clearAllAccountData() {
  try {
    // 清除 cookies
    document.cookie.split(";").forEach(function (cookie) {
      document.cookie = cookie.replace(/^ +/, "").replace(/=.*/, "=;expires=Sun, 03 Aug 2025 05:31:44 GMT;path=/");
    });
    
    // 清除存储
    localStorage.clear();
    sessionStorage.clear();
    return true;
  } catch (error) {
    console.error("Failed to clear account data:", error);
    return false;
  }
}

/**
 * 获取用户信息
 */
async function getUserInfo() {
  try {
    const userInfo = { followCount: null, fansCount: null, username: null };
    const headerMenu = document.querySelector('[id*="douyin-header-menu"]');
    
    if (headerMenu) {
      // 获取关注数
      const followSpan = Array.from(headerMenu.getElementsByTagName("span"))
        .find(element => "关注" === element.textContent);
      if (followSpan) {
        userInfo.followCount = followSpan.nextElementSibling 
          ? followSpan.nextElementSibling.textContent.trim() 
          : null;
      }
      
      // 获取粉丝数
      const fansSpan = Array.from(headerMenu.getElementsByTagName("span"))
        .find(element => "粉丝" === element.textContent);
      if (fansSpan) {
        userInfo.fansCount = fansSpan.nextElementSibling 
          ? fansSpan.nextElementSibling.textContent.trim() 
          : null;
      }
      
      // 获取用户名
      const userContainer = followSpan ? followSpan.closest("div") : null;
      if (userContainer) {
        const userLink = userContainer.parentElement.querySelector("a");
        if (userLink) {
          userInfo.username = userLink.textContent.trim();
        }
      }
    }
    
    return userInfo;
  } catch (error) {
    console.error("Failed to get user info:", error);
    return { followCount: null, fansCount: null, username: null };
  }
}

/**
 * 等待登录状态确认
 */
async function waitForLoginConfirmation(accountId) {
  try {
    const userInfo = await getUserInfo();
    if (userInfo && userInfo.username) {
      ipcRenderer.send("login-status", true);
    } else {
      await randomDelay(2200, 2800);
      await waitForLoginConfirmation(accountId);
    }
  } catch (error) {
    console.error("Login confirmation error:", error);
  }
}

/**
 * 检查登录状态
 */
async function checkLoginStatus(accountData) {
  try {
    const parsedData = typeof accountData === "string" ? JSON.parse(accountData) : accountData;
    const accountId = parsedData.id;
    currentWindowId = accountId;
    
    const windowId = sessionStorage.getItem("WIN_ID");
    let accountRow = await ipcRenderer.invoke("account-row-action", {
      action: "get",
      id: windowId
    });
    
    const options = accountRow?.options;
    if (options) {
      options.status = "检查登录状态";
      options.lastOprateTime = Date.now();
      await ipcRenderer.invoke("account-row-action", {
        action: "set",
        id: windowId,
        newRow: { options }
      });
    }
    
    await waitForLoginConfirmation(accountId);
    return true;
  } catch (error) {
    console.error("Check login status error:", error);
    return false;
  }
}

/**
 * 跳转到搜索页面
 */
async function goSearchPage(searchData) {
  try {
    const windowId = sessionStorage.getItem("WIN_ID");
    let accountRow = await ipcRenderer.invoke("account-row-action", {
      action: "get",
      id: windowId
    });
    
    const options = accountRow?.options;
    if (options) {
      options.status = "跳转搜索页面";
      options.lastOprateTime = Date.now();
      await ipcRenderer.invoke("account-row-action", {
        action: "set",
        id: windowId,
        newRow: { options }
      });
    }
    
    const parsedData = typeof searchData === "string" ? JSON.parse(searchData) : searchData;
    await navigateToSearch(parsedData.keyword);
    return true;
  } catch (error) {
    console.error("Go search page error:", error);
    return false;
  }
}

/**
 * 跳转到用户页面
 */
async function goIdPage(userData) {
  try {
    const windowId = sessionStorage.getItem("WIN_ID");
    let accountRow = await ipcRenderer.invoke("account-row-action", {
      action: "get",
      id: windowId
    });
    
    const options = accountRow?.options;
    if (options) {
      options.status = "获取粉丝中...";
      options.lastOprateTime = Date.now();
      await ipcRenderer.invoke("account-row-action", {
        action: "set",
        id: windowId,
        newRow: { options }
      });
    }
    
    if (await waitForElement("div", userData)) {
      await randomDelay(2200, 2800);
      const fansData = await getFansData();
      if (fansData) {
        return true;
      }
    }
    return false;
  } catch (error) {
    console.error("Go ID page error:", error);
    return false;
  }
}

/**
 * 养号滑动操作
 */
async function yjSwipe(operationData) {
  try {
    const parsedData = typeof operationData === "string" ? JSON.parse(operationData) : operationData;
    const { type, rowId } = parsedData;

    const actionNames = {
      swipe: "仅观看滑动",
      dianzan: "点赞",
      pinglun: "评论",
      shoucan: "收藏"
    };

    const waitTime = randomInt(3000, 10000);
    const waitSeconds = (waitTime / 1000).toFixed(1);

    let accountRow = await ipcRenderer.invoke("account-row-action", {
      action: "get",
      id: rowId
    });

    const options = accountRow?.options;
    if (options) {
      options.status = `养号-${actionNames[type]}-停留 ${waitSeconds}-秒`;
      options.lastOprateTime = Date.now();
      await ipcRenderer.invoke("account-row-action", {
        action: "set",
        id: rowId,
        newRow: { options }
      });
    }

    // 等待指定时间
    await new Promise(resolve => setTimeout(resolve, waitTime));

    // 执行对应操作
    if (type === "dianzan") {
      if (accountRow.bandDianzanNum) {
        showToast("无法点赞，不操作！");
      } else {
        // 模拟按键 Z
        document.dispatchEvent(new KeyboardEvent("keydown", {
          key: "z", code: "KeyZ", bubbles: true
        }));
        await randomDelay(4000, 10000);
        document.dispatchEvent(new KeyboardEvent("keyup", {
          key: "z", code: "KeyZ", bubbles: true
        }));
        await randomDelay(4000, 10000);
      }
    }

    if (type === "pinglun") {
      if (accountRow.bandPinglunNum) {
        showToast("无法评论，不操作！");
      } else {
        // 模拟按键 X 打开评论
        document.dispatchEvent(new KeyboardEvent("keydown", {
          key: "x", code: "KeyX", bubbles: true
        }));
        await randomDelay(4000, 10000);
        document.dispatchEvent(new KeyboardEvent("keyup", {
          key: "x", code: "KeyX", bubbles: true
        }));
        await randomDelay(4000, 10000);

        // 点击评论输入框
        const commentInput = document.querySelector(".comment-input-inner-container");
        if (commentInput) {
          const rect = commentInput.getBoundingClientRect();
          const randomOffset = (min, max) => Math.random() * (max - min) + min;
          const x = rect.left + rect.width / 2 + randomOffset(-10, 10);
          const y = rect.top + rect.height / 2 + randomOffset(-5, 5);

          ["mousedown", "mouseup", "click"].forEach(eventType => {
            commentInput.dispatchEvent(new MouseEvent(eventType, {
              bubbles: true,
              cancelable: true,
              view: window,
              clientX: x,
              clientY: y
            }));
          });

          await randomDelay(4000, 10000);

          // 聚焦到编辑器
          const editor = document.querySelector('.public-DraftEditor-content[contenteditable="true"]');
          if (editor) {
            editor.focus();
            await randomDelay(4000, 10000);

            // 调用评论粘贴
            await ipcRenderer.invoke("pasteComment", {
              id: rowId,
              comment: "很棒"
            });
            await randomDelay(4000, 10000);
          }
        }
      }
    }

    if (type === "shoucan") {
      if (accountRow.bandShoucanNum) {
        showToast("无法收藏，不操作！");
      } else {
        // 模拟按键 C
        document.dispatchEvent(new KeyboardEvent("keydown", {
          key: "c", code: "KeyC", bubbles: true
        }));
        await randomDelay(4000, 10000);
        document.dispatchEvent(new KeyboardEvent("keyup", {
          key: "c", code: "KeyC", bubbles: true
        }));
        await randomDelay(4000, 10000);
      }
    }

    // 滑动到下一个视频
    await randomDelay(4000, 10000);
    const downArrowEvent = new KeyboardEvent("keydown", {
      key: "ArrowDown",
      code: "ArrowDown",
      keyCode: 40,
      which: 40,
      bubbles: true,
      cancelable: true
    });
    document.dispatchEvent(downArrowEvent);

  } catch (error) {
    console.error("YJ swipe error:", error);
  }
}

/**
 * 跳转到粉丝页面
 */
async function goFansPage(userData) {
  try {
    const windowId = sessionStorage.getItem("WIN_ID");
    let accountRow = await ipcRenderer.invoke("account-row-action", {
      action: "get",
      id: windowId
    });

    const options = accountRow?.options;
    if (options) {
      options.status = "进入粉丝主页";
      options.lastOprateTime = Date.now();
      await ipcRenderer.invoke("account-row-action", {
        action: "set",
        id: windowId,
        newRow: { options }
      });
    }

    return true;
  } catch (error) {
    console.error("Go fans page error:", error);
    return false;
  }
}

/**
 * 粉丝页面操作
 */
async function fansPageOperate(operationData) {
  try {
    const parsedData = typeof operationData === "string" ? JSON.parse(operationData) : operationData;
    const accountId = parsedData.id;

    let accountRow = await ipcRenderer.invoke("account-row-action", {
      action: "get",
      id: accountId
    });

    const options = accountRow?.options;
    if (options) {
      options.status = "粉丝页面操作中...";
      options.lastOprateTime = Date.now();
      await ipcRenderer.invoke("account-row-action", {
        action: "set",
        id: accountId,
        newRow: { options }
      });
    }

    // 执行粉丝页面的具体操作逻辑
    // 这里可以添加更多具体的操作

    return true;
  } catch (error) {
    console.error("Fans page operate error:", error);
    return false;
  }
}

// ===== 辅助函数 =====

/**
 * 等待元素出现
 */
async function waitForElement(selector, data, maxRetries = 10) {
  try {
    let retries = maxRetries;
    while (retries > 0) {
      const element = document.querySelector(selector);
      if (element) {
        return true;
      }
      await randomDelay(1000, 2000);
      retries--;
    }
    return false;
  } catch (error) {
    console.error("Wait for element error:", error);
    return false;
  }
}

/**
 * 获取粉丝数据
 */
async function getFansData() {
  try {
    const fansElement = Array.from(document.querySelectorAll("div"))
      .find(element => element.textContent === "粉丝");

    if (fansElement) {
      const fansCountElement = fansElement.nextElementSibling;
      if (fansCountElement) {
        return {
          count: fansCountElement.textContent.trim(),
          element: fansElement
        };
      }
    }
    return null;
  } catch (error) {
    console.error("Get fans data error:", error);
    return null;
  }
}

/**
 * 导航到搜索页面
 */
async function navigateToSearch(keyword) {
  try {
    window.location.replace("https://www.douyin.com/search/" + keyword);
  } catch (error) {
    console.error("Navigate to search error:", error);
  }
}

/**
 * 导航到指定URL
 */
async function navigateToUrl(url) {
  try {
    window.location.replace(url);
  } catch (error) {
    console.error("Navigate to URL error:", error);
  }
}

/**
 * 查找粉丝列表
 */
async function findFansList(userData) {
  try {
    const spans = document.querySelectorAll("span");
    let fansListData = null;

    for (const span of spans) {
      if (span.textContent && span.textContent.includes("粉丝")) {
        // 处理粉丝列表逻辑
        fansListData = {
          element: span,
          count: span.nextElementSibling?.textContent || "0"
        };
        break;
      }
    }

    return fansListData;
  } catch (error) {
    console.error("Find fans list error:", error);
    return null;
  }
}

// ===== electron API 暴露 =====

contextBridge.exposeInMainWorld("electron", {
  // 清除账号数据
  clearAllAccountData,

  // 登录状态检查
  checkLoginStatus,

  // 页面跳转
  goSearchPage,
  goIdPage,
  goFansPage,

  // 养号操作
  yjSwipe,

  // 粉丝页面操作
  FansPageOprate: fansPageOperate,

  // 辅助功能
  getUserInfo,
  waitForLoginConfirmation,
  findFansList,
  navigateToSearch,
  navigateToUrl
});

// ===== URL 监听 =====

/**
 * URL 变化监听
 */
function handleUrlChange() {
  try {
    if (location.href !== currentUrl) {
      currentUrl = location.href;
      urlChangeTime = Date.now();
    }
  } catch (error) {
    console.error("URL change handling error:", error);
  }
}

// 监听 URL 变化
window.addEventListener("hashchange", handleUrlChange);
window.addEventListener("popstate", handleUrlChange);
setInterval(handleUrlChange, 1000);

// ===== 弹窗处理 =====

/**
 * 弹窗观察器
 */
function startPopupObserver() {
  try {
    const observer = new MutationObserver(async () => {
      // 处理"我知道了"按钮
      try {
        const buttons = document.querySelectorAll(".semi-button-content");
        for (const button of Array.from(buttons)) {
          if (button instanceof HTMLElement &&
              button.innerText.trim() === "我知道了") {
            await clickElement(button);
            break;
          }
        }
      } catch (error) {
        console.error("Button handling error:", error);
      }

      // 检查各种弹窗
      const verifyPanel = document.querySelector(".second-verify-panel");
      const loginDialog = document.querySelector("#douyin-login-new-id");
      const captchaFrame = Array.from(document.querySelectorAll("iframe"))
        .find(frame => frame.src && frame.src.includes("verifycenter/captcha"));

      const hasPopup = !!(verifyPanel || captchaFrame);

      // 处理登录对话框
      if (loginDialog && !isLoginDialogProcessing) {
        isLoginDialogProcessing = true;
        setTimeout(() => {
          if (loginDialog) {
            const hasUnloginText = Array.from(document.querySelectorAll("div"))
              .some(element =>
                (element.innerText && element.innerText.includes("未登录")) ||
                (element.textContent && element.textContent.includes("未登录"))
              );

            if (!hasUnloginText) {
              loginDialog.remove();
              isLoginDialogHandled = false;
            }
          }
          isLoginDialogProcessing = false;
        }, 10000);
      }

      // 处理弹窗暂停
      if (hasPopup) {
        const windowId = sessionStorage.getItem("WIN_ID");
        if (windowId && !isPopupPaused) {
          isPopupPaused = true;
          ipcRenderer.send("pauseAutomationDueToPopup", windowId);
        }
      } else {
        if (isPopupPaused) {
          const windowId = sessionStorage.getItem("WIN_ID");
          if (windowId) {
            isPopupPaused = false;
            ipcRenderer.send("resumeAutomationDueToPopup", windowId);
          }
        }
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true
    });
  } catch (error) {
    console.error("Popup observer error:", error);
  }
}

// ===== 账号超时检查 =====

// 定时检查账号操作超时
setInterval(async () => {
  try {
    const windowId = sessionStorage.getItem("WIN_ID");
    if (!windowId) return;

    const accountData = await ipcRenderer.invoke("account-row-action", {
      action: "get",
      id: windowId
    });

    if (!accountData) return;

    let lastOperateTime = accountData.options?.lastOprateTime;
    if (!lastOperateTime) {
      lastOperateTime = Date.now();
      await ipcRenderer.invoke("account-row-action", {
        action: "set",
        id: windowId,
        newRow: {
          options: {
            ...accountData.options,
            lastOprateTime: lastOperateTime
          }
        }
      });
      return;
    }

    // 检查是否超时（30秒）
    if (Date.now() - lastOperateTime > 30000) {
      if (accountData.isPaused) {
        lastOperateTime = Date.now();
        return;
      }

      lastOperateTime = Date.now();
      await ipcRenderer.invoke("account-row-action", {
        action: "set",
        id: windowId,
        newRow: {
          options: {
            ...accountData.options,
            lastOprateTime: lastOperateTime
          }
        }
      });

      // 发送超时信号
      ipcRenderer.send("account-timeout", { id: windowId });
    }
  } catch (error) {
    console.error("Account timeout check error:", error);
  }
}, 10000); // 每10秒检查一次

// ===== 链接拦截 =====

// 拦截 bitbrowser 协议链接
document.addEventListener("click", function(event) {
  const target = event.target;
  const linkElement = target?.closest?.("a");

  if (linkElement &&
      typeof linkElement.href === "string" &&
      linkElement.href.startsWith("bitbrowser://")) {
    event.preventDefault();
    event.stopPropagation();
  }
}, true);

// ===== electronAPI 暴露 =====

contextBridge.exposeInMainWorld("electronAPI", {
  /**
   * 获取代理IP地址
   */
  getProxyIp: async function() {
    try {
      const response = await fetch("http://ip-api.com/json");
      const data = await response.json();
      return data.query;
    } catch (error) {
      console.error("Failed to get proxy IP:", error);
      return null;
    }
  },

  /**
   * 启动弹窗观察器
   */
  startPopupObserver: startPopupObserver,

  /**
   * 检查是否有弹窗
   */
  checkPopup: () => !!document.querySelector(".second-verify-panel"),

  /**
   * 获取账号行数据
   */
  getRow: (accountId) => ipcRenderer.invoke("get-account-row", accountId),

  /**
   * 更新账号行数据
   */
  updateRow: ({ id, newRow }) => ipcRenderer.invoke("update-account-row", { id, newRow })
});

// ===== hardware API 暴露 =====

contextBridge.exposeInMainWorld("hardware", {
  /**
   * 获取设备ID
   */
  getDeviceId: async () => await ipcRenderer.invoke("get-device-id")
});

// ===== 右键菜单禁用 =====

window.addEventListener("contextmenu", event => {
  event.preventDefault();
});
