/**
 * Electron 预加载脚本 - 抖音自动化工具
 * 完整重构版本，包含所有原始功能
 * 版本: 2.5.0
 */

"use strict";

// ===== 依赖导入 =====
const { ipc<PERSON>enderer, contextBridge } = require("electron");

// ===== 全局变量 =====
let currentWindowId;
let isPopupPaused = false;
let currentUrl = location.href;
let urlChangeTime = Date.now();
let lastOperationTime = Date.now();
let isToastHandled = false;
let isLoginDialogHandled = false;
let isLoginDialogProcessing = false;

// ===== 工具函数 =====

/**
 * DOM 操作工具
 */
const domUtils = {
  append(parent, child) {
    if (!Array.from(parent.children).find(element => element === child)) {
      return parent.appendChild(child);
    }
  },
  remove(parent, child) {
    if (Array.from(parent.children).find(element => element === child)) {
      return parent.removeChild(child);
    }
  }
};

/**
 * 随机延迟函数
 */
const randomDelay = (min, max) => new Promise(resolve => {
  const delay = Math.floor(Math.random() * (max - min + 1)) + min;
  setTimeout(resolve, delay);
});

/**
 * 显示提示消息
 */
function showToast(message) {
  let toastElement = document.createElement("div");
  toastElement.innerText = message;
  Object.assign(toastElement.style, {
    position: "fixed",
    top: "20%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    background: "rgba(0,0,0,0.85)",
    color: "#fff",
    padding: "16px 32px",
    borderRadius: "8px",
    fontSize: "20px",
    zIndex: 99999,
    boxShadow: "0 2px 16px rgba(0,0,0,0.2)",
    textAlign: "center",
    pointerEvents: "none"
  });
  
  document.body.appendChild(toastElement);
  setTimeout(() => {
    toastElement.remove();
  }, 2200);
}

/**
 * 生成随机数
 */
function randomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// ===== IPC 通信设置 =====

// 监听窗口ID设置
ipcRenderer.on("set-win-id", (_, windowId) => {
  try {
    currentWindowId = windowId;
    sessionStorage.setItem("WIN_ID", windowId);
  } catch (error) {
    console.error("Failed to set window ID:", error);
  }
});

// 暴露 IPC 通信接口
contextBridge.exposeInMainWorld("ipcRenderer", {
  on(...args) {
    const [channel, listener] = args;
    return ipcRenderer.on(channel, (event, ...data) => listener(event, ...data));
  },
  off(...args) {
    const [channel, ...rest] = args;
    return ipcRenderer.off(channel, ...rest);
  },
  send(...args) {
    const [channel, ...data] = args;
    return ipcRenderer.send(channel, ...data);
  },
  invoke(...args) {
    const [channel, ...data] = args;
    return ipcRenderer.invoke(channel, ...data);
  }
});

// ===== 核心业务功能 =====

/**
 * 清除所有账号数据
 */
async function clearAllAccountData() {
  try {
    // 清除 cookies
    document.cookie.split(";").forEach(function (cookie) {
      document.cookie = cookie.replace(/^ +/, "").replace(/=.*/, "=;expires=Sun, 03 Aug 2025 05:31:44 GMT;path=/");
    });
    
    // 清除存储
    localStorage.clear();
    sessionStorage.clear();
    return true;
  } catch (error) {
    console.error("Failed to clear account data:", error);
    return false;
  }
}

/**
 * 获取用户信息
 */
async function getUserInfo() {
  try {
    const userInfo = { followCount: null, fansCount: null, username: null };
    const headerMenu = document.querySelector('[id*="douyin-header-menu"]');
    
    if (headerMenu) {
      // 获取关注数
      const followSpan = Array.from(headerMenu.getElementsByTagName("span"))
        .find(element => "关注" === element.textContent);
      if (followSpan) {
        userInfo.followCount = followSpan.nextElementSibling 
          ? followSpan.nextElementSibling.textContent.trim() 
          : null;
      }
      
      // 获取粉丝数
      const fansSpan = Array.from(headerMenu.getElementsByTagName("span"))
        .find(element => "粉丝" === element.textContent);
      if (fansSpan) {
        userInfo.fansCount = fansSpan.nextElementSibling 
          ? fansSpan.nextElementSibling.textContent.trim() 
          : null;
      }
      
      // 获取用户名
      const userContainer = followSpan ? followSpan.closest("div") : null;
      if (userContainer) {
        const userLink = userContainer.parentElement.querySelector("a");
        if (userLink) {
          userInfo.username = userLink.textContent.trim();
        }
      }
    }
    
    return userInfo;
  } catch (error) {
    console.error("Failed to get user info:", error);
    return { followCount: null, fansCount: null, username: null };
  }
}

/**
 * 等待登录状态确认
 */
async function waitForLoginConfirmation(accountId) {
  try {
    const userInfo = await getUserInfo();
    if (userInfo && userInfo.username) {
      ipcRenderer.send("login-status", true);
    } else {
      await randomDelay(2200, 2800);
      await waitForLoginConfirmation(accountId);
    }
  } catch (error) {
    console.error("Login confirmation error:", error);
  }
}

/**
 * 检查登录状态
 */
async function checkLoginStatus(accountData) {
  try {
    const data = typeof accountData === "string" ? JSON.parse(accountData) : accountData;
    const { id: accountId, rowId } = data;
    
    currentWindowId = accountId;
    sessionStorage.setItem("WIN_ID", accountId);
    
    let accountRow = await ipcRenderer.invoke("account-row-action", { action: "get", id: rowId });
    const options = accountRow?.options;
    
    if (options) {
      options.status = "检查登陆状态";
      options.lastOprateTime = Date.now();
      await ipcRenderer.invoke("account-row-action", { 
        action: "set", 
        id: rowId, 
        newRow: { options } 
      });
      
      await waitForLoginConfirmation(rowId);
      
      const userInfo = await getUserInfo();
      options.guanzhuNum = userInfo.followCount;
      options.fensiNum = userInfo.fansCount;
      options.userName = userInfo.username;
      options.status = "已登陆账号";
      options.lastOprateTime = Date.now();
      
      await ipcRenderer.invoke("account-row-action", { 
        action: "set", 
        id: rowId, 
        newRow: { options } 
      });
    }
    
    if (accountId === "home") {
      navigateToUrl("https://www.douyin.com/?recommend=1&from_nav=1");
    } else {
      await searchForAccount(accountId);
    }
    
    return true;
  } catch (error) {
    console.error("Check login status error:", error);
    return false;
  }
}

/**
 * 导航到指定URL
 */
async function navigateToUrl(url) {
  try {
    window.location.replace(url);
  } catch (error) {
    console.error("Navigation error:", error);
  }
}

/**
 * 搜索抖音账号
 */
async function searchForAccount(accountId) {
  try {
    window.location.replace("https://www.douyin.com/search/" + accountId);
  } catch (error) {
    console.error("Search error:", error);
  }
}

/**
 * 等待元素出现
 */
async function waitForElement(selector, text, maxRetries = 10) {
  try {
    let retries = maxRetries;
    while (retries > 0) {
      const elements = document.querySelectorAll(selector);
      let found = false;

      Array.from(elements).forEach(element => {
        if (element.textContent.includes(text)) {
          found = true;
        }
      });

      if (found) {
        return true;
      }

      await randomDelay(2000, 3000);
      retries--;
    }
    return false;
  } catch (error) {
    console.error("Wait for element error:", error);
    return false;
  }
}

/**
 * 搜索页面操作
 */
async function goSearchPage(searchText) {
  try {
    const windowId = sessionStorage.getItem("WIN_ID");
    let accountRow = await ipcRenderer.invoke("account-row-action", { action: "get", id: windowId });
    const options = accountRow?.options;

    if (options) {
      options.status = "搜索抖音号";
      options.lastOprateTime = Date.now();
      await ipcRenderer.invoke("account-row-action", {
        action: "set",
        id: windowId,
        newRow: { options }
      });
    }

    if (await waitForElement("span", searchText)) {
      await randomDelay(1200, 1800);
      const searchResult = await findSearchResult(searchText);
      await randomDelay(1200, 1800);
      return searchResult;
    }

    return false;
  } catch (error) {
    console.error("Go search page error:", error);
    return false;
  }
}

/**
 * 查找搜索结果
 */
async function findSearchResult(searchText) {
  try {
    const spans = document.querySelectorAll("span");
    let targetSpan = null;

    spans.forEach(span => {
      if (span.textContent.includes(searchText)) {
        targetSpan = span;
      }
    });

    if (!targetSpan) {
      return null;
    }

    const container = targetSpan.closest("div");
    const link = container ? container.querySelector("a") : null;

    if (link) {
      return link.getAttribute("href");
    }

    return null;
  } catch (error) {
    console.error("Find search result error:", error);
    return null;
  }
}

/**
 * 进入ID页面
 */
async function goIdPage(accountId) {
  try {
    const windowId = sessionStorage.getItem("WIN_ID");
    let accountRow = await ipcRenderer.invoke("account-row-action", { action: "get", id: windowId });
    const options = accountRow?.options;

    if (options) {
      options.status = "获取粉丝中...";
      options.lastOprateTime = Date.now();
      await ipcRenderer.invoke("account-row-action", {
        action: "set",
        id: windowId,
        newRow: { options }
      });
    }

    if (await waitForElement("div", accountId)) {
      await randomDelay(2200, 2800);
      const fansPageResult = await goToFansPage();
      if (fansPageResult) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error("Go ID page error:", error);
    return false;
  }
}

/**
 * 进入粉丝页面
 */
async function goToFansPage() {
  try {
    const fansElement = Array.from(document.querySelectorAll("div"))
      .find(element => "粉丝" === element.textContent);

    if (fansElement) {
      const container = fansElement.closest("div");
      if (container) {
        await randomDelay(2200, 2800);
        const fansData = await collectFansData(container);
        if (fansData) {
          return true;
        }
      }
      return false;
    }

    return false;
  } catch (error) {
    console.error("Go to fans page error:", error);
    return false;
  }
}

/**
 * 收集粉丝数据
 */
async function collectFansData(fansContainer) {
  try {
    let retries = 0;
    let fansListContainer = null;

    // 查找粉丝列表容器
    while (retries < 5) {
      await clickElement(fansContainer);
      await randomDelay(2200, 2800);

      const allElements = document.querySelectorAll("*");
      fansListContainer = Array.from(allElements).find(element =>
        element.getAttributeNames().some(attr =>
          attr.startsWith("data-") &&
          "user-fans-container" === element.getAttribute(attr)
        )
      );

      if (fansListContainer) {
        break;
      }

      retries++;
      await randomDelay(1000, 1500);
    }

    if (!fansListContainer) {
      return false;
    }

    // 滚动收集粉丝链接
    const windowId = sessionStorage.getItem("WIN_ID");
    let accountRow = await ipcRenderer.invoke("account-row-action", { action: "get", id: windowId });
    let maxFans = Math.floor(Math.random() * 301) + 500;

    if (accountRow.runMode === 0) {
      maxFans = Math.floor(Math.random() * 13) + 13;
    }

    const fansLinks = new Set();
    let scrollHeight = fansListContainer.scrollHeight;

    while (fansListContainer.scrollTop + fansListContainer.clientHeight < scrollHeight) {
      const remainingHeight = scrollHeight - fansListContainer.scrollTop;
      let scrollAmount = remainingHeight * (Math.random() * 0.3 + 0.6);
      scrollAmount = Math.min(scrollAmount, 300);

      const scrollDuration = Math.random() * 1000 + 1000;
      fansListContainer.scrollTop += scrollAmount;

      await randomDelay(scrollDuration - 500, scrollDuration + 500);

      // 更新操作时间
      const currentWindowId = sessionStorage.getItem("WIN_ID");
      let currentAccountRow = await ipcRenderer.invoke("account-row-action", { action: "get", id: currentWindowId });
      const currentOptions = currentAccountRow?.options;
      if (currentOptions) {
        currentOptions.lastOprateTime = Date.now();
        await ipcRenderer.invoke("account-row-action", {
          action: "set",
          id: currentWindowId,
          newRow: { options: currentOptions }
        });
      }

      // 收集当前可见的粉丝链接
      Array.from(fansListContainer.querySelectorAll("a")).forEach(link => {
        const href = link.getAttribute("href");
        if (href) {
          fansLinks.add("https:" + href + "?from_tab_name=main&is_search=0&list_name=fans&nt=0");
        }
      });

      const newScrollHeight = fansListContainer.scrollHeight;
      if (newScrollHeight !== scrollHeight) {
        scrollHeight = newScrollHeight;
      }

      if (fansLinks.size >= maxFans ||
          fansListContainer.scrollTop + fansListContainer.clientHeight >= scrollHeight) {
        break;
      }
    }

    let fansArray = Array.from(fansLinks);
    if (accountRow.runMode === 0) {
      fansArray = fansArray.slice(0, maxFans);
    }

    return fansArray;
  } catch (error) {
    console.error("Collect fans data error:", error);
    return false;
  }
}

/**
 * 点击元素
 */
async function clickElement(element) {
  try {
    const clickEvent = new MouseEvent("click", {
      bubbles: true,
      cancelable: true,
      view: window
    });
    element.dispatchEvent(clickEvent);
  } catch (error) {
    console.error("Click element error:", error);
  }
}

/**
 * 养号滑动操作
 */
async function performYangHaoSwipe(operationData) {
  try {
    const data = typeof operationData === "string" ? JSON.parse(operationData) : operationData;
    const { type, rowId } = data;

    const operationNames = {
      swipe: "仅观看滑动",
      dianzan: "点赞",
      pinglun: "评论",
      shoucan: "收藏"
    };

    const waitTime = randomInt(3000, 10000);
    const waitTimeSeconds = (waitTime / 1000).toFixed(1);

    let accountRow = await ipcRenderer.invoke("account-row-action", { action: "get", id: rowId });
    const options = accountRow?.options;

    if (options) {
      options.status = "养号-" + operationNames[type] + "-停留 " + waitTimeSeconds + "-秒";
      options.lastOprateTime = Date.now();
      await ipcRenderer.invoke("account-row-action", {
        action: "set",
        id: rowId,
        newRow: { options }
      });
    }

    // 等待指定时间
    await new Promise(resolve => setTimeout(resolve, waitTime));

    // 执行对应操作
    if (type === "dianzan") {
      if (accountRow.bandDianzanNum) {
        showToast("无法点赞，不操作！");
      } else {
        await simulateKeyPress("z");
      }
    } else if (type === "pinglun") {
      if (accountRow.bandPinglunNum) {
        showToast("无法评论，不操作！");
      } else {
        await simulateKeyPress("x");
        await handleCommentInput(rowId);
      }
    } else if (type === "shoucan") {
      if (accountRow.bandShoucanNum) {
        showToast("无法收藏，不操作！");
      } else {
        await simulateKeyPress("c");
      }
    }

    await randomDelay(4000, 10000);

    // 滑动到下一个视频
    const downArrowEvent = new KeyboardEvent("keydown", {
      key: "ArrowDown",
      code: "ArrowDown",
      keyCode: 40,
      which: 40,
      bubbles: true,
      cancelable: true
    });
    document.dispatchEvent(downArrowEvent);

  } catch (error) {
    console.error("Yang hao swipe error:", error);
  }
}

/**
 * 模拟按键操作
 */
async function simulateKeyPress(key) {
  const keyCode = key.toUpperCase();

  document.dispatchEvent(new KeyboardEvent("keydown", {
    key: key,
    code: "Key" + keyCode,
    bubbles: true
  }));

  await randomDelay(4000, 10000);

  document.dispatchEvent(new KeyboardEvent("keyup", {
    key: key,
    code: "Key" + keyCode,
    bubbles: true
  }));

  await randomDelay(4000, 10000);
}

/**
 * 处理评论输入
 */
async function handleCommentInput(rowId) {
  try {
    const commentContainer = document.querySelector(".comment-input-inner-container");
    if (!commentContainer) {
      return;
    }

    const rect = commentContainer.getBoundingClientRect();
    const randomOffset = (min, max) => Math.random() * (max - min) + min;
    const clickX = rect.left + rect.width / 2 + randomOffset(-10, 10);
    const clickY = rect.top + rect.height / 2 + randomOffset(-5, 5);

    // 模拟点击评论框
    ["mousedown", "mouseup", "click"].forEach(eventType => {
      commentContainer.dispatchEvent(new MouseEvent(eventType, {
        bubbles: true,
        cancelable: true,
        view: window,
        clientX: clickX,
        clientY: clickY
      }));
    });

    await randomDelay(4000, 10000);

    // 聚焦到编辑器
    const editor = document.querySelector('.public-DraftEditor-content[contenteditable="true"]');
    if (!editor) {
      return;
    }

    editor.focus();
    await randomDelay(4000, 10000);

    // 调用主进程粘贴评论
    await ipcRenderer.invoke("pasteComment", { id: rowId, comment: "很棒" });
    await randomDelay(4000, 10000);

  } catch (error) {
    console.error("Handle comment input error:", error);
  }
}

/**
 * 进入粉丝主页
 */
async function goFansPage(url) {
  try {
    const windowId = sessionStorage.getItem("WIN_ID");
    let accountRow = await ipcRenderer.invoke("account-row-action", { action: "get", id: windowId });
    const options = accountRow?.options;

    if (options) {
      options.status = "进入粉丝主页";
      options.lastOprateTime = Date.now();
      await ipcRenderer.invoke("account-row-action", {
        action: "set",
        id: windowId,
        newRow: { options }
      });
    }

    await navigateToUrl(url);
    return true;
  } catch (error) {
    console.error("Go fans page error:", error);
    return false;
  }
}

// ===== 上下文桥接 =====

// 暴露主要功能到渲染进程
contextBridge.exposeInMainWorld("electron", {
  clearAllAccountData,
  checkLoginStatus,
  goSearchPage,
  goIdPage,
  yjSwipe: performYangHaoSwipe,
  goFansPage,
  FansPageOprate: async (data) => {
    // 粉丝页面操作的复杂逻辑
    try {
      const operationData = typeof data === "string" ? JSON.parse(data) : data;
      const { id } = operationData;

      let accountRow = await ipcRenderer.invoke("account-row-action", { action: "get", id });
      const options = accountRow?.options || operationData.options;

      // 选择操作模块的逻辑
      const moduleResult = await selectOperationModule(options.tongjiNum, {});
      const operationNames = { guanzhu: "关注", dianzan: "点赞", pinglun: "评论", shoucan: "收藏" };

      if (moduleResult.selectedModule) {
        options.status = "打开作品-" + operationNames[moduleResult.selectedModule];
        options.lastOprateTime = Date.now();
        await ipcRenderer.invoke("account-row-action", {
          action: "set",
          id,
          newRow: { options }
        });
      }

      const workElement = await getRandomWork();
      await randomDelay(2200, 2800);

      if (workElement) {
        await clickElement(workElement);
        await randomDelay(2200, 2800);

        // 执行对应操作
        if (moduleResult.selectedModule === "guanzhu") {
          await simulateKeyPress("g");
        } else if (moduleResult.selectedModule === "dianzan") {
          if (accountRow.bandDianzanNum) {
            showToast("无法点赞，不操作！");
          } else {
            await simulateKeyPress("z");
          }
        } else if (moduleResult.selectedModule === "pinglun") {
          if (accountRow.bandPinglunNum) {
            showToast("无法评论，不操作！");
          } else {
            await simulateKeyPress("x");
            await handleCommentInput(operationData.id);
          }
        } else if (moduleResult.selectedModule === "shoucan") {
          if (accountRow.bandShoucanNum) {
            showToast("无法收藏，不操作！");
          } else {
            await simulateKeyPress("c");
          }
        }

        // 重置或更新统计
        if (moduleResult.selectedModule === null && options?.tongjiNum) {
          Object.keys(options.tongjiNum).forEach(key => {
            if (options.tongjiNum[key] && typeof options.tongjiNum[key] === "object") {
              options.tongjiNum[key].cur = 0;
            }
          });
          options.status = "当前总任务数完成-进行重置";
          options.lastOprateTime = Date.now();
          await ipcRenderer.invoke("account-row-action", {
            action: "set",
            id,
            newRow: { options }
          });
        } else {
          options.tongjiNum = moduleResult.newState;
        }

        // 更新用户信息
        const userInfo = await getUserInfo();
        if (options?.tongjiNum) {
          options.guanzhuNum = userInfo.followCount;
          options.fensiNum = userInfo.fansCount;
          options.userName = userInfo.username;
          options.jintianFans = Number(options.fensiNum) - Number(options.todayStartFans);
        }

        options.lastOprateTime = Date.now();
        await ipcRenderer.invoke("account-row-action", {
          action: "set",
          id,
          newRow: { options }
        });

        await randomDelay(3200, 4800);
        return true;
      }

      return false;
    } catch (error) {
      console.error("Fans page operation error:", error);
      return false;
    }
  }
});

/**
 * 选择操作模块
 */
async function selectOperationModule(moduleStats, preferences) {
  try {
    preferences = preferences || {};
    let totalTasks = 0;
    let completedTasks = 0;

    // 计算总任务数和已完成数
    for (const module in moduleStats) {
      totalTasks += moduleStats[module].total;
      completedTasks += moduleStats[module].cur;
    }

    // 首次选择评论
    if (sessionStorage.getItem("isFirstSelect") !== "false" && moduleStats.pinglun) {
      sessionStorage.setItem("isFirstSelect", "false");
      moduleStats.pinglun.cur += 1;
      return { newState: moduleStats, selectedModule: "pinglun" };
    }

    const completionRate = completedTasks / totalTasks;
    const availableModules = [];
    let totalWeight = 0;

    // 构建可用模块列表
    for (const module in moduleStats) {
      const cur = moduleStats[module].cur;
      const total = moduleStats[module].total;
      const moduleCompletionRate = cur / total;

      if (cur < total && !(module === "tuwen" && preferences.tuwen !== true)) {
        if (preferences[module] && moduleCompletionRate < completionRate) {
          availableModules.push({ name: module, weight: total * 2 });
          totalWeight += total * 2;
        } else {
          availableModules.push({ name: module, weight: total });
          totalWeight += total;
        }
      }
    }

    if (availableModules.length === 0) {
      return { newState: moduleStats, selectedModule: null };
    }

    // 随机选择模块
    const randomValue = Math.random() * totalWeight;
    let currentWeight = 0;
    let selectedModule = null;

    for (let i = 0; i < availableModules.length; i++) {
      currentWeight += availableModules[i].weight;
      if (randomValue <= currentWeight) {
        selectedModule = availableModules[i].name;
        break;
      }
    }

    if (selectedModule) {
      moduleStats[selectedModule].cur += 1;
    }

    return { newState: moduleStats, selectedModule };
  } catch (error) {
    console.error("Select operation module error:", error);
    return { newState: moduleStats, selectedModule: null };
  }
}

/**
 * 获取随机作品
 */
async function getRandomWork() {
  try {
    let retries = 0;
    let scrollListContainer = null;

    // 查找滚动列表容器
    while (retries < 5) {
      const allElements = document.querySelectorAll("*");
      scrollListContainer = Array.from(allElements).find(element =>
        element.getAttributeNames().some(attr =>
          attr.startsWith("data-") &&
          "scroll-list" === element.getAttribute(attr)
        )
      );

      if (scrollListContainer) {
        break;
      }

      retries++;
      await randomDelay(1000, 1500);
    }

    if (!scrollListContainer) {
      return false;
    }

    let listItems = scrollListContainer.querySelectorAll("li");

    // 等待列表项加载
    for (let i = 0; i < 3 && listItems.length === 0; i++) {
      await randomDelay(1000, 1500);
      listItems = scrollListContainer.querySelectorAll("li");
    }

    if (listItems.length === 0) {
      return false;
    }

    // 选择前3个作品中的随机一个
    const topItems = Array.from(listItems).slice(0, 3);
    if (topItems.length === 0) {
      return false;
    }

    const randomItem = topItems[Math.floor(Math.random() * topItems.length)];
    return randomItem.querySelector("a");
  } catch (error) {
    console.error("Get random work error:", error);
    return false;
  }
}

// ===== 更多上下文桥接 =====

// 暴露硬件信息接口
contextBridge.exposeInMainWorld("hardware", {
  getDeviceId: async () => await ipcRenderer.invoke("get-device-id")
});

// 暴露电子API
contextBridge.exposeInMainWorld("electronAPI", {
  getProxyIp: async function() {
    try {
      const response = await fetch("http://ip-api.com/json");
      return (await response.json()).query;
    } catch (error) {
      console.error("Get proxy IP error:", error);
      return null;
    }
  },
  startPopupObserver: startPopupObserver,
  checkPopup: () => !!document.querySelector(".second-verify-panel"),
  getRow: (id) => ipcRenderer.invoke("get-account-row", id),
  updateRow: ({ id, newRow }) => ipcRenderer.invoke("update-account-row", { id, newRow })
});

// ===== 页面监听器 =====

/**
 * URL 变化监听
 */
function trackUrlChanges() {
  try {
    if (location.href !== currentUrl) {
      currentUrl = location.href;
      urlChangeTime = Date.now();
    }
  } catch (error) {
    console.error("Track URL changes error:", error);
  }
}

// 监听 URL 变化
window.addEventListener("hashchange", trackUrlChanges);
window.addEventListener("popstate", trackUrlChanges);
setInterval(trackUrlChanges, 1000);

/**
 * 弹窗观察器
 */
function startPopupObserver() {
  try {
    new MutationObserver(async () => {
      // 处理"我知道了"按钮
      await handleKnowButton();

      // 检查验证弹窗
      const verifyPanel = document.querySelector(".second-verify-panel");
      const loginDialog = document.querySelector("#douyin-login-new-id");
      const captchaFrame = Array.from(document.querySelectorAll("iframe"))
        .find(frame => frame.src && frame.src.includes("verifycenter/captcha"));

      const hasPopup = !!(verifyPanel || captchaFrame);

      // 处理登录对话框
      if (loginDialog && !isLoginDialogHandled) {
        isLoginDialogHandled = true;
        setTimeout(() => {
          if (loginDialog && !checkUnloggedStatus()) {
            loginDialog.remove();
            isPopupPaused = false;
          }
          isLoginDialogHandled = false;
        }, 10000);
      }

      // 处理验证弹窗
      if (hasPopup) {
        const windowId = sessionStorage.getItem("WIN_ID");
        let accountRow = await ipcRenderer.invoke("account-row-action", { action: "get", id: windowId });
        const options = accountRow?.options;
        if (options) {
          options.lastOprateTime = Date.now();
          await ipcRenderer.invoke("account-row-action", {
            action: "set",
            id: windowId,
            newRow: { options }
          });
        }

        urlChangeTime = Date.now();

        if (!isPopupPaused && currentWindowId) {
          ipcRenderer.send("pauseAutomationDueToPopup", currentWindowId);

          const currentOptions = accountRow?.options;
          if (currentOptions) {
            currentOptions.status = "有弹窗需要处理，请扫码或拖动，完成后等待20秒自动恢复流程";
            await ipcRenderer.invoke("account-row-action", {
              action: "set",
              id: windowId,
              newRow: { options: currentOptions }
            });
          }
          isPopupPaused = true;
        }
        lastOperationTime = Date.now();
      } else {
        if (isPopupPaused && currentWindowId) {
          ipcRenderer.send("resumeAutomationDueToPopup", currentWindowId);
          isPopupPaused = false;
        }
        lastOperationTime = Date.now();
      }

      // 处理功能限制提示
      await handleFunctionRestrictions();

    }).observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      characterData: true,
      attributeFilter: ["class"]
    });
  } catch (error) {
    console.error("Start popup observer error:", error);
  }
}

/**
 * 处理"我知道了"按钮
 */
async function handleKnowButton() {
  try {
    const knowButtons = document.querySelectorAll(".semi-button-content");
    for (const button of Array.from(knowButtons)) {
      if (button instanceof HTMLElement && "我知道了" === button.innerText.trim()) {
        await clickElement(button);
        break;
      }
    }
  } catch (error) {
    console.error("Handle know button error:", error);
  }
}

/**
 * 检查未登录状态
 */
function checkUnloggedStatus() {
  try {
    return Array.from(document.querySelectorAll("div")).some(element =>
      (element.innerText && element.innerText.includes("未登录")) ||
      (element.textContent && element.textContent.includes("未登录"))
    );
  } catch (error) {
    console.error("Check unlogged status error:", error);
    return false;
  }
}

/**
 * 处理功能限制提示
 */
async function handleFunctionRestrictions() {
  try {
    const toastContainer = document.querySelector("#toastContainer");
    if (toastContainer && !isToastHandled) {
      let restrictionType = null;

      if (toastContainer.innerText.includes("点赞功能已封禁") ||
          toastContainer.innerText.includes("点赞速度太快啦")) {
        restrictionType = "dianzan";
      } else if (toastContainer.innerText.includes("收藏功能已封禁")) {
        restrictionType = "shoucang";
      } else if (toastContainer.innerText.includes("评论功能受限")) {
        restrictionType = "pinglun";
      }

      if (restrictionType && (isToastHandled = true, currentWindowId)) {
        const windowId = sessionStorage.getItem("WIN_ID");
        let accountRow = await ipcRenderer.invoke("account-row-action", { action: "get", id: windowId });

        if (restrictionType === "dianzan") {
          accountRow.bandDianzanNum = Number(accountRow.bandDianzanNum || 0) + 1;
        } else if (restrictionType === "shoucang") {
          accountRow.bandShoucanNum = Number(accountRow.bandShoucanNum || 0) + 1;
        } else if (restrictionType === "pinglun") {
          accountRow.bandPinglunNum = Number(accountRow.bandPinglunNum || 0) + 1;
        }

        await ipcRenderer.invoke("account-row-action", {
          action: "set",
          id: windowId,
          newRow: accountRow
        });
        isPopupPaused = true;
      }
    }

    // 重置提示处理状态
    if (!(toastContainer && (
      toastContainer.innerText.includes("点赞功能已封禁") ||
      toastContainer.innerText.includes("点赞速度太快啦") ||
      toastContainer.innerText.includes("收藏功能已封禁") ||
      toastContainer.innerText.includes("评论功能受限")
    ))) {
      isToastHandled = false;
    }
  } catch (error) {
    console.error("Handle function restrictions error:", error);
  }
}

// ===== 初始化 =====

// 启动弹窗观察器
if (document.readyState === "loading") {
  try {
    window.addEventListener("DOMContentLoaded", startPopupObserver);
  } catch (error) {
    console.error("DOM content loaded error:", error);
  }
} else {
  startPopupObserver();
}

// 定时检查账号超时
setInterval(async () => {
  try {
    const windowId = sessionStorage.getItem("WIN_ID");
    if (!windowId) {
      return;
    }

    let accountRow = await ipcRenderer.invoke("account-row-action", { action: "get", id: windowId });
    let lastOperateTime = accountRow?.options?.lastOprateTime;

    if (!lastOperateTime) {
      lastOperateTime = Date.now();
      await ipcRenderer.invoke("account-row-action", {
        action: "set",
        id: windowId,
        newRow: {
          options: {
            ...accountRow.options,
            lastOprateTime: lastOperateTime
          }
        }
      });
      return;
    }

    if (Date.now() - lastOperateTime > 30000) {
      if (accountRow.isPaused) {
        lastOperateTime = Date.now();
        return;
      }

      lastOperateTime = Date.now();
      await ipcRenderer.invoke("account-row-action", {
        action: "set",
        id: windowId,
        newRow: {
          options: {
            ...accountRow.options,
            lastOprateTime: lastOperateTime
          }
        }
      });

      ipcRenderer.send("account-timeout", { id: windowId });
    }
  } catch (error) {
    console.error("Account timeout check error:", error);
  }
}, 10000);

// 阻止 bitbrowser 协议链接
document.addEventListener("click", function(event) {
  const link = event.target?.closest?.("a");
  if (link && typeof link.href === "string" && link.href.startsWith("bitbrowser://")) {
    event.preventDefault();
    event.stopPropagation();
  }
}, true);

// 禁用右键菜单
window.addEventListener("contextmenu", event => {
  event.preventDefault();
});

// ===== 指纹注入监听 =====

// 监听指纹注入
ipcRenderer.on("inject-fingerprint", (_, fingerprint) => {
  try {
    injectFingerprint(fingerprint);
  } catch (error) {
    console.error("Inject fingerprint error:", error);
  }
});

/**
 * 注入浏览器指纹
 */
function injectFingerprint(fingerprint) {
  try {
    if (fingerprint.userAgent) {
      Object.defineProperty(navigator, "userAgent", {
        get: () => fingerprint.userAgent,
        configurable: true
      });
    }

    if (fingerprint.language) {
      Object.defineProperty(navigator, "language", {
        get: () => fingerprint.language,
        configurable: true
      });
      Object.defineProperty(navigator, "languages", {
        get: () => [fingerprint.language],
        configurable: true
      });
    }

    if (fingerprint.screen) {
      Object.defineProperty(window.screen, "width", {
        get: () => fingerprint.screen.width,
        configurable: true
      });
      Object.defineProperty(window.screen, "height", {
        get: () => fingerprint.screen.height,
        configurable: true
      });
      Object.defineProperty(window.screen, "availWidth", {
        get: () => fingerprint.screen.width,
        configurable: true
      });
      Object.defineProperty(window.screen, "availHeight", {
        get: () => fingerprint.screen.height,
        configurable: true
      });
    }

    if (fingerprint.timezone) {
      try {
        const OriginalDateTimeFormat = Intl.DateTimeFormat;
        Intl.DateTimeFormat = class extends OriginalDateTimeFormat {
          constructor(locales, options) {
            options = options || {};
            options.timeZone = fingerprint.timezone;
            super(locales, options);
          }
        };
      } catch (error) {
        console.error("Timezone injection error:", error);
      }
    }

    if (fingerprint.deviceMemory) {
      Object.defineProperty(navigator, "deviceMemory", {
        get: () => Number(fingerprint.deviceMemory),
        configurable: true
      });
    }

    if (fingerprint.hardwareConcurrency) {
      Object.defineProperty(navigator, "hardwareConcurrency", {
        get: () => Number(fingerprint.hardwareConcurrency),
        configurable: true
      });
    }

    if (fingerprint.geolocation) {
      const { lat, lon } = fingerprint.geolocation;
      const position = {
        coords: {
          latitude: lat,
          longitude: lon,
          accuracy: 50,
          altitude: null,
          altitudeAccuracy: null,
          heading: null,
          speed: null
        },
        timestamp: Date.now()
      };

      navigator.geolocation.getCurrentPosition = function(success, error) {
        success(position);
      };

      navigator.geolocation.watchPosition = function(success, error) {
        success(position);
        return 1;
      };
    }

    if (fingerprint.webglVendor || fingerprint.webglRenderer) {
      const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
      WebGLRenderingContext.prototype.getParameter = function(parameter) {
        if (fingerprint.webglVendor && parameter === 37445) {
          return fingerprint.webglVendor;
        } else if (fingerprint.webglRenderer && parameter === 37446) {
          return fingerprint.webglRenderer;
        } else {
          return originalGetParameter.apply(this, arguments);
        }
      };
    }

    // 隐藏 webdriver 标识
    Object.defineProperty(navigator, "webdriver", {
      get: () => false,
      configurable: true
    });

  } catch (error) {
    console.error("Fingerprint injection error:", error);
  }
}

// ===== 加载动画 =====

const { appendLoading, removeLoading } = (function() {
  const LOADERS_CSS = "loaders-css__square-spin";
  const css = `
@keyframes square-spin {
  25% { transform: perspective(100px) rotateX(180deg) rotateY(0); }
  50% { transform: perspective(100px) rotateX(180deg) rotateY(180deg); }
  75% { transform: perspective(100px) rotateX(0) rotateY(180deg); }
  100% { transform: perspective(100px) rotateX(0) rotateY(0); }
}
.loaders-css__square-spin > div {
  animation-fill-mode: both;
  width: 50px;
  height: 50px;
  background: #fff;
  animation: square-spin 3s 0s cubic-bezier(0.09, 0.57, 0.49, 0.9) infinite;
}
.app-loading-wrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #282c34;
  z-index: 9;
}`;

  const styleElement = document.createElement("style");
  const loadingElement = document.createElement("div");

  styleElement.id = "app-loading-style";
  styleElement.innerHTML = css;
  loadingElement.className = "app-loading-wrap";
  loadingElement.innerHTML = '<div class="loaders-css__square-spin"><div></div></div>';

  return {
    appendLoading() {
      domUtils.append(document.head, styleElement);
      domUtils.append(document.body, loadingElement);
    },
    removeLoading() {
      domUtils.remove(document.head, styleElement);
      domUtils.remove(document.body, loadingElement);
    }
  };
})();

// 页面加载完成后显示加载动画
(function(readyStates = ["complete", "interactive"]) {
  return new Promise(resolve => {
    if (readyStates.includes(document.readyState)) {
      resolve(true);
    } else {
      document.addEventListener("readystatechange", () => {
        if (readyStates.includes(document.readyState)) {
          resolve(true);
        }
      });
    }
  });
}()).then(appendLoading);

// 监听移除加载动画消息
window.onmessage = event => {
  if (event.data.payload === "removeLoading") {
    removeLoading();
  }
};

// 4.999秒后自动移除加载动画
setTimeout(removeLoading, 4999);

// ===== URL 监听 =====

/**
 * URL 变化监听
 */
function handleUrlChange() {
  try {
    if (location.href !== currentUrl) {
      currentUrl = location.href;
      urlChangeTime = Date.now();
    }
  } catch (error) {
    console.error("URL change handling error:", error);
  }
}

// 监听 URL 变化
window.addEventListener("hashchange", handleUrlChange);
window.addEventListener("popstate", handleUrlChange);
setInterval(handleUrlChange, 1000);

// ===== 弹窗处理 =====

/**
 * 弹窗观察器
 */
function startPopupObserver() {
  try {
    const observer = new MutationObserver(async () => {
      // 处理"我知道了"按钮
      try {
        const buttons = document.querySelectorAll(".semi-button-content");
        for (const button of Array.from(buttons)) {
          if (button instanceof HTMLElement &&
              button.innerText.trim() === "我知道了") {
            await clickElement(button);
            break;
          }
        }
      } catch (error) {
        console.error("Button handling error:", error);
      }

      // 检查各种弹窗
      const verifyPanel = document.querySelector(".second-verify-panel");
      const loginDialog = document.querySelector("#douyin-login-new-id");
      const captchaFrame = Array.from(document.querySelectorAll("iframe"))
        .find(frame => frame.src && frame.src.includes("verifycenter/captcha"));

      const hasPopup = !!(verifyPanel || captchaFrame);

      // 处理登录对话框
      if (loginDialog && !isLoginDialogProcessing) {
        isLoginDialogProcessing = true;
        setTimeout(() => {
          if (loginDialog) {
            const hasUnloginText = Array.from(document.querySelectorAll("div"))
              .some(element =>
                (element.innerText && element.innerText.includes("未登录")) ||
                (element.textContent && element.textContent.includes("未登录"))
              );

            if (!hasUnloginText) {
              loginDialog.remove();
              isLoginDialogHandled = false;
            }
          }
          isLoginDialogProcessing = false;
        }, 10000);
      }

      // 处理弹窗暂停
      if (hasPopup) {
        const windowId = sessionStorage.getItem("WIN_ID");
        if (windowId && !isPopupPaused) {
          isPopupPaused = true;
          ipcRenderer.send("pauseAutomationDueToPopup", windowId);
        }
      } else {
        if (isPopupPaused) {
          const windowId = sessionStorage.getItem("WIN_ID");
          if (windowId) {
            isPopupPaused = false;
            ipcRenderer.send("resumeAutomationDueToPopup", windowId);
          }
        }
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true
    });
  } catch (error) {
    console.error("Popup observer error:", error);
  }
}

// ===== 账号超时检查 =====

// 定时检查账号操作超时
setInterval(async () => {
  try {
    const windowId = sessionStorage.getItem("WIN_ID");
    if (!windowId) return;

    const accountData = await ipcRenderer.invoke("account-row-action", {
      action: "get",
      id: windowId
    });

    if (!accountData) return;

    let lastOperateTime = accountData.options?.lastOprateTime;
    if (!lastOperateTime) {
      lastOperateTime = Date.now();
      await ipcRenderer.invoke("account-row-action", {
        action: "set",
        id: windowId,
        newRow: {
          options: {
            ...accountData.options,
            lastOprateTime: lastOperateTime
          }
        }
      });
      return;
    }

    // 检查是否超时（30秒）
    if (Date.now() - lastOperateTime > 30000) {
      if (accountData.isPaused) {
        lastOperateTime = Date.now();
        return;
      }

      lastOperateTime = Date.now();
      await ipcRenderer.invoke("account-row-action", {
        action: "set",
        id: windowId,
        newRow: {
          options: {
            ...accountData.options,
            lastOprateTime: lastOperateTime
          }
        }
      });

      // 发送超时信号
      ipcRenderer.send("account-timeout", { id: windowId });
    }
  } catch (error) {
    console.error("Account timeout check error:", error);
  }
}, 10000); // 每10秒检查一次

// ===== 链接拦截 =====

// 拦截 bitbrowser 协议链接
document.addEventListener("click", function(event) {
  const target = event.target;
  const linkElement = target?.closest?.("a");

  if (linkElement &&
      typeof linkElement.href === "string" &&
      linkElement.href.startsWith("bitbrowser://")) {
    event.preventDefault();
    event.stopPropagation();
  }
}, true);

// ===== electronAPI 暴露 =====

contextBridge.exposeInMainWorld("electronAPI", {
  /**
   * 获取代理IP地址
   */
  getProxyIp: async function() {
    try {
      const response = await fetch("http://ip-api.com/json");
      const data = await response.json();
      return data.query;
    } catch (error) {
      console.error("Failed to get proxy IP:", error);
      return null;
    }
  },

  /**
   * 启动弹窗观察器
   */
  startPopupObserver: startPopupObserver,

  /**
   * 检查是否有弹窗
   */
  checkPopup: () => !!document.querySelector(".second-verify-panel"),

  /**
   * 获取账号行数据
   */
  getRow: (accountId) => ipcRenderer.invoke("get-account-row", accountId),

  /**
   * 更新账号行数据
   */
  updateRow: ({ id, newRow }) => ipcRenderer.invoke("update-account-row", { id, newRow })
});

// ===== hardware API 暴露 =====

contextBridge.exposeInMainWorld("hardware", {
  /**
   * 获取设备ID
   */
  getDeviceId: async () => await ipcRenderer.invoke("get-device-id")
});

// ===== 右键菜单禁用 =====

window.addEventListener("contextmenu", event => {
  event.preventDefault();
});
